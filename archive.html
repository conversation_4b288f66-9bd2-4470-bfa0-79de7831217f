<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Archive</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: none;
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button:active {
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button:active {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }

            .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        .archived-habit {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.4), inset -3px -3px 6px rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.7;
        }

        @media (prefers-color-scheme: dark) {
            .archived-habit {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.6), inset -3px -3px 6px rgba(51, 51, 51, 0.2);
            }
        }

        .archive-actions {
            position: relative;
        }

        .archive-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
            min-width: 140px;
            z-index: 1000;
            display: none;
            margin-top: 8px;
        }

        @media (prefers-color-scheme: dark) {
            .archive-dropdown {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }

        .archive-dropdown.show {
            display: block;
            animation: fadeIn 0.2s ease-out;
        }

        .archive-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .archive-dropdown-item:first-child {
            border-radius: 12px 12px 0 0;
        }

        .archive-dropdown-item:last-child {
            border-radius: 0 0 12px 12px;
        }

        .archive-dropdown-item:only-child {
            border-radius: 12px;
        }

        .archive-dropdown-item:hover {
            background: rgba(163, 177, 198, 0.1);
        }

        .archive-dropdown-item.restore {
            color: var(--success);
        }

        .archive-dropdown-item.delete {
            color: var(--warning);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tab-item {
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
        }

        .tab-item:hover {
            background: rgba(163, 177, 198, 0.1);
        }

        .tab-item:active {
            transform: scale(0.95);
        }
    </style>
</head>

<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">

            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">

                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <button class="neumorphic-button w-10 h-10 flex items-center justify-center" onclick="goBack()">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </button>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">Archive</h1>
                        <p class="text-xs" style="color: var(--text-secondary);">Archived habits</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center" onclick="clearAllArchive()">
                        <i class="fas fa-trash text-sm" style="color: var(--warning);"></i>
                    </div>
                </div>

                <!-- Archived Habits List -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="space-y-3" id="archivedHabitsList">
                        <!-- Archived habits will be dynamically loaded here -->
                    </div>
                    
                    <!-- Empty state -->
                    <div class="text-center py-8" id="emptyState" style="display: none;">
                        <div class="neumorphic-button w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-archive text-2xl" style="color: var(--text-muted);"></i>
                        </div>
                        <h3 class="font-semibold mb-2" style="color: var(--text-primary);">No archived habits</h3>
                        <p class="text-sm" style="color: var(--text-secondary);">Habits you archive will appear here</p>
                    </div>
                </div>

                <!-- Tab Bar -->
                <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                    <div class="flex justify-around items-center">
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('dashboard')">
                            <i class="fas fa-home text-lg" style="color: var(--text-secondary);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-secondary);">Home</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('progress')">
                            <i class="fas fa-chart-line text-lg" style="color: var(--text-secondary);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-secondary);">Progress</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('archive')">
                            <i class="fas fa-archive text-lg" style="color: var(--success);"></i>
                            <span class="text-xs mt-1" style="color: var(--success);">Archive</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('settings')">
                            <i class="fas fa-cog text-lg" style="color: var(--text-secondary);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-secondary);">Settings</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include habit grid component -->
    <script src="habit-grid.js"></script>

    <script>
        // Initialize habit grid component
        const habitGrid = new HabitGrid();

        // Load archived habits on page load
        document.addEventListener('DOMContentLoaded', function () {
            loadArchivedHabits();
        });

        // Load and display archived habits
        function loadArchivedHabits() {
            try {
                const archivedHabits = JSON.parse(localStorage.getItem('habitTracker_archivedHabits') || '[]');
                const archivedHabitsList = document.getElementById('archivedHabitsList');
                const emptyState = document.getElementById('emptyState');

                if (archivedHabits.length === 0) {
                    emptyState.style.display = 'block';
                    archivedHabitsList.style.display = 'none';
                } else {
                    emptyState.style.display = 'none';
                    archivedHabitsList.style.display = 'block';
                    
                    // Sort by archive date (newest first)
                    archivedHabits.sort((a, b) => new Date(b.archivedAt) - new Date(a.archivedAt));
                    
                    archivedHabitsList.innerHTML = archivedHabits.map(habit => {
                        const archiveDate = new Date(habit.archivedAt).toLocaleDateString();
                        return `
                            <div class="archived-habit p-3" data-habit-id="${habit.id}">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center">
                                            <i class="fas fa-archive text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium" style="color: var(--text-primary);">${habit.name}</h4>
                                            <p class="text-xs" style="color: var(--text-secondary);">Archived on ${archiveDate}</p>
                                        </div>
                                    </div>
                                    <div class="archive-actions">
                                        <button class="neumorphic-button w-8 h-8 flex items-center justify-center" onclick="toggleArchiveMenu(${habit.id})">
                                            <i class="fas fa-ellipsis-vertical text-sm" style="color: var(--text-primary);"></i>
                                        </button>
                                        <div class="archive-dropdown" id="archiveMenu${habit.id}">
                                            <div class="archive-dropdown-item restore" onclick="restoreHabit(${habit.id}); hideArchiveMenu(${habit.id});">
                                                <i class="fas fa-undo text-xs"></i>
                                                <span class="text-sm">Restore</span>
                                            </div>
                                            <div class="archive-dropdown-item delete" onclick="deleteHabit(${habit.id}); hideArchiveMenu(${habit.id});">
                                                <i class="fas fa-trash text-xs"></i>
                                                <span class="text-sm">Delete</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="habit-grid-container" id="archivedHabitGrid${habit.id}">
                                    <!-- Grid will be generated by JavaScript -->
                                </div>
                            </div>
                        `;
                    }).join('');
                    
                    // Generate grids for all archived habits
                    generateArchivedHabitGrids(archivedHabits);
                }
            } catch (error) {
                console.error('Error loading archived habits:', error);
            }
        }

        // Generate habit grids for archived habits
        function generateArchivedHabitGrids(archivedHabits) {
            archivedHabits.forEach(habit => {
                const container = document.getElementById(`archivedHabitGrid${habit.id}`);
                if (container && habit.history) {
                    // Generate grid with the archived habit's history
                    habitGrid.generateHabitGrid(`archivedHabitGrid${habit.id}`, habit, {
                        autoScroll: true,
                        readOnly: true // Make it read-only since it's archived
                    });
                }
            });
        }

        // Toggle archive menu dropdown
        function toggleArchiveMenu(habitId) {
            const menu = document.getElementById(`archiveMenu${habitId}`);
            const isCurrentlyOpen = menu.classList.contains('show');
            
            // Close all other open menus first
            document.querySelectorAll('.archive-dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
            
            // Toggle current menu
            if (!isCurrentlyOpen) {
                menu.classList.add('show');
            }
        }

        function hideArchiveMenu(habitId) {
            const menu = document.getElementById(`archiveMenu${habitId}`);
            menu.classList.remove('show');
        }

        // Close menus when clicking elsewhere
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.archive-actions') && !event.target.closest('.archive-dropdown')) {
                document.querySelectorAll('.archive-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // Restore habit to active list
        function restoreHabit(habitId) {
            try {
                let archivedHabits = JSON.parse(localStorage.getItem('habitTracker_archivedHabits') || '[]');
                let activeHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                
                const habitIndex = archivedHabits.findIndex(h => h.id === habitId);
                if (habitIndex !== -1) {
                    const habitToRestore = archivedHabits[habitIndex];
                    
                    // Remove archive-specific properties
                    delete habitToRestore.archivedAt;
                    
                    // Add back to active habits
                    activeHabits.push(habitToRestore);
                    
                    // Remove from archived habits
                    archivedHabits.splice(habitIndex, 1);
                    
                    // Update localStorage
                    localStorage.setItem('habitTracker_habits', JSON.stringify(activeHabits));
                    localStorage.setItem('habitTracker_archivedHabits', JSON.stringify(archivedHabits));
                    
                    // Refresh the list
                    loadArchivedHabits();
                    
                    // Show success message
                    showNotification(`✅ "${habitToRestore.name}" has been restored`);
                }
            } catch (error) {
                console.error('Error restoring habit:', error);
                alert('Error restoring habit. Please try again.');
            }
        }

        // Permanently delete habit
        function deleteHabit(habitId) {
            try {
                let archivedHabits = JSON.parse(localStorage.getItem('habitTracker_archivedHabits') || '[]');
                const habitIndex = archivedHabits.findIndex(h => h.id === habitId);
                
                if (habitIndex !== -1) {
                    const habitToDelete = archivedHabits[habitIndex];
                    
                    if (confirm(`Are you sure you want to permanently delete "${habitToDelete.name}"? This action cannot be undone.`)) {
                        // Remove from archived habits
                        archivedHabits.splice(habitIndex, 1);
                        
                        // Update localStorage
                        localStorage.setItem('habitTracker_archivedHabits', JSON.stringify(archivedHabits));
                        
                        // Refresh the list
                        loadArchivedHabits();
                        
                        // Show success message
                        showNotification(`🗑️ "${habitToDelete.name}" has been permanently deleted`);
                    }
                }
            } catch (error) {
                console.error('Error deleting habit:', error);
                alert('Error deleting habit. Please try again.');
            }
        }

        // Clear all archived habits
        function clearAllArchive() {
            try {
                const archivedHabits = JSON.parse(localStorage.getItem('habitTracker_archivedHabits') || '[]');
                
                if (archivedHabits.length === 0) {
                    alert('No archived habits to clear.');
                    return;
                }
                
                if (confirm(`Are you sure you want to permanently delete all ${archivedHabits.length} archived habits? This action cannot be undone.`)) {
                    localStorage.setItem('habitTracker_archivedHabits', '[]');
                    loadArchivedHabits();
                    showNotification('🗑️ All archived habits have been cleared');
                }
            } catch (error) {
                console.error('Error clearing archive:', error);
                alert('Error clearing archive. Please try again.');
            }
        }

        // Navigation functions
        function navigateToPage(page) {
            switch (page) {
                case 'dashboard':
                    window.location.href = 'dashboard.html';
                    break;
                case 'progress':
                    window.location.href = 'progress.html';
                    break;
                case 'archive':
                    // Already on archive page
                    break;
                case 'settings':
                    window.location.href = 'settings.html';
                    break;
            }
        }

        function goBack() {
            window.location.href = 'dashboard.html';
        }

        // Show notification
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--bg-primary);
                color: var(--text-primary);
                padding: 12px 20px;
                border-radius: 15px;
                box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
                z-index: 1000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                text-align: center;
                transition: all 0.3s ease;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(-50%) translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>

</html>
