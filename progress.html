<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Progress</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
            --chart-line: #cd7f32;
            --chart-bg: rgba(205, 127, 50, 0.1);
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
                --chart-line: #ff9f5c;
                --chart-bg: rgba(255, 159, 92, 0.1);
            }
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-inset {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: inset 4px 4px 8px rgba(163, 177, 198, 0.6), inset -4px -4px 8px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-inset {
                box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.8), inset -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }
        
        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
            .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            background: conic-gradient(var(--success) 84%, var(--bg-primary) 84%);
            border-radius: 50%;
            position: relative;
            box-shadow: inset 6px 6px 12px rgba(163, 177, 198, 0.6), inset -6px -6px 12px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-ring {
                box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.8), inset -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-ring::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 90px;
            height: 90px;
            background: var(--bg-primary);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-ring::after {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .tab-button-new {
            background: transparent;
            border-radius: 6px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 6px 12px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-button-new.active {
            background: var(--chart-line);
            color: white !important;
            box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
        }
        
        .tab-button-new:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .chart-container-new {
            position: relative;
            height: 180px;
            overflow: hidden;
            touch-action: pan-x;
            border-radius: 16px;
        }
        
        .chart-area-new {
            position: relative;
            width: 100%;
            height: 140px;
            background: var(--chart-bg);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .chart-grid-new {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.15;
        }
        
        .chart-grid-line-new {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--text-muted);
        }
        
        .chart-svg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
        
        .line-path-new {
            fill: none;
            stroke: var(--chart-line);
            stroke-width: 2.8;
            stroke-linecap: round;
            stroke-linejoin: round;
            filter: drop-shadow(0 1px 3px rgba(205, 127, 50, 0.4));
        }
        
        .chart-fill-new {
            fill: url(#chartGradient);
            opacity: 0.4;
        }
        
        .chart-labels-new {
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }
        
        .swipe-dot-new {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--text-muted);
            opacity: 0.4;
            transition: all 0.3s ease;
        }
        
        .swipe-dot-new.active {
            background: var(--chart-line);
            opacity: 1;
            transform: scale(1.2);
        }
        
        .habit-progress-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid;
            border-color: rgba(163, 177, 198, 0.2);
        }
        
        @media (prefers-color-scheme: dark) {
            .habit-progress-item {
                border-color: rgba(255, 255, 255, 0.1);
            }
        }
        
        .habit-progress-item:last-child {
            border-bottom: none;
        }
        
        .habit-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-primary);
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .habit-icon {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-bar {
            flex: 1;
            height: 6px;
            background: var(--bg-primary);
            border-radius: 3px;
            margin: 0 12px;
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
            overflow: hidden;
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-bar {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">
            
            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">
                
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">Progress</h1>
                        <p class="text-sm" style="color: var(--text-secondary);">Your journey insights</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>

                <!-- Overall Consistency Score -->
                <div class="neumorphic-card p-6 mb-6 text-center">
                    <div class="progress-ring mx-auto mb-4 flex items-center justify-center">
                        <div class="relative z-10 text-center">
                            <div class="text-2xl font-bold" style="color: var(--text-primary);">84%</div>
                            <div class="text-xs" style="color: var(--text-secondary);">Consistency</div>
                        </div>
                    </div>
                    <h3 class="text-lg font-semibold mb-2" style="color: var(--text-primary);">Great Progress!</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">You're building strong habits consistently</p>
                </div>

                <!-- Key Metrics -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="neumorphic-card p-4 text-center">
                        <div class="text-2xl font-bold mb-1" style="color: var(--success);">17</div>
                        <div class="text-sm" style="color: var(--text-secondary);">Current Streak</div>
                    </div>
                    <div class="neumorphic-card p-4 text-center">
                        <div class="text-2xl font-bold mb-1" style="color: var(--info);">23</div>
                        <div class="text-sm" style="color: var(--text-secondary);">Best Streak</div>
                    </div>
                    <div class="neumorphic-card p-4 text-center">
                        <div class="text-2xl font-bold mb-1" style="color: var(--wellness);">142</div>
                        <div class="text-sm" style="color: var(--text-secondary);">Total Days</div>
                    </div>
                    <div class="neumorphic-card p-4 text-center">
                        <div class="text-2xl font-bold mb-1" style="color: var(--warning);">8</div>
                        <div class="text-sm" style="color: var(--text-secondary);">Achievements</div>
                    </div>
                </div>

                <!-- Completion Chart -->
                <div class="neumorphic-card p-5 mb-6">
                    <!-- Header with tabs -->
                    <div class="flex items-center justify-between mb-5">
                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Completion</h3>
                        <div class="flex bg-white/20 rounded-lg p-1 space-x-1">
                            <button class="tab-button-new" data-period="week" style="color: var(--text-secondary);">Week</button>
                            <button class="tab-button-new" data-period="month" style="color: var(--text-secondary);">Month</button>
                            <button class="tab-button-new active" data-period="year" style="color: white;">Year</button>
                        </div>
                    </div>
                    
                    <!-- Chart Container -->
                    <div class="chart-container-new neumorphic-inset p-4 relative">
                        <!-- Swipe dots -->
                        <div class="absolute top-3 right-3 flex space-x-1.5">
                            <div class="swipe-dot-new active"></div>
                            <div class="swipe-dot-new"></div>
                            <div class="swipe-dot-new"></div>
                        </div>
                        
                        <!-- Chart area -->
                        <div class="chart-area-new">
                            <!-- Grid lines - minimal -->
                            <div class="chart-grid-new">
                                <div class="chart-grid-line-new" style="top: 20%;"></div>
                                <div class="chart-grid-line-new" style="top: 40%;"></div>
                                <div class="chart-grid-line-new" style="top: 60%;"></div>
                                <div class="chart-grid-line-new" style="top: 80%;"></div>
                            </div>
                            
                            <!-- SVG Chart -->
                            <svg class="chart-svg" viewBox="0 0 280 140" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--chart-line); stop-opacity: 0.2;"/>
                                        <stop offset="100%" style="stop-color: var(--chart-line); stop-opacity: 0.0;"/>
                                    </linearGradient>
                                </defs>
                                
                                <!-- Fill area under the line -->
                                <path class="chart-fill-new" d="M 20 140 L 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75 L 260 140 Z"/>
                                
                                <!-- Main line -->
                                <path class="line-path-new" d="M 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75"/>
                                
                                <!-- Data points -->
                                <circle cx="20" cy="110" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="60" cy="95" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="100" cy="105" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="140" cy="85" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="180" cy="80" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="220" cy="90" r="2.5" fill="var(--chart-line)"/>
                                <circle cx="260" cy="75" r="2.5" fill="var(--chart-line)"/>
                            </svg>
                        </div>
                        
                        <!-- Chart labels - positioned better -->
                        <div class="chart-labels-new">
                            <span>2018</span>
                            <span>2019</span>
                            <span>2020</span>
                            <span>2021</span>
                            <span>2022</span>
                            <span>2023</span>
                            <span>2024</span>
                        </div>
                    </div>
                </div>

                <!-- Individual Habit Performance -->
                <div class="neumorphic-card p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Habit Performance</h3>
                    
                    <div class="space-y-4">
                        <div class="habit-progress-item">
                            <div class="flex items-center">
                                <div class="habit-icon">
                                    <i class="fas fa-dumbbell text-sm" style="color: var(--success);"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Push-ups</h4>
                                    <p class="text-xs" style="color: var(--text-secondary);">5 per day</p>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 89%; background: linear-gradient(90deg, var(--success), var(--wellness));"></div>
                            </div>
                            <span class="text-sm font-medium" style="color: var(--text-primary);">89%</span>
                        </div>

                        <div class="habit-progress-item">
                            <div class="flex items-center">
                                <div class="habit-icon">
                                    <i class="fas fa-glass-water text-sm" style="color: var(--info);"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Water</h4>
                                    <p class="text-xs" style="color: var(--text-secondary);">1 glass</p>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 95%; background: linear-gradient(90deg, var(--info), var(--wellness));"></div>
                            </div>
                            <span class="text-sm font-medium" style="color: var(--text-primary);">95%</span>
                        </div>

                        <div class="habit-progress-item">
                            <div class="flex items-center">
                                <div class="habit-icon">
                                    <i class="fas fa-book text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Reading</h4>
                                    <p class="text-xs" style="color: var(--text-secondary);">1 page</p>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 78%; background: linear-gradient(90deg, var(--warning), var(--mindfulness));"></div>
                            </div>
                            <span class="text-sm font-medium" style="color: var(--text-primary);">78%</span>
                        </div>
                    </div>
                </div>

                <!-- Achievements -->
                <div class="neumorphic-card p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Recent Achievements</h3>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div class="neumorphic-inset p-3 text-center">
                            <div class="neumorphic-button w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                                <i class="fas fa-fire text-xs" style="color: var(--warning);"></i>
                            </div>
                            <div class="text-xs font-medium" style="color: var(--text-primary);">Week Warrior</div>
                            <div class="text-xs" style="color: var(--text-secondary);">7-day streak</div>
                        </div>
                        
                        <div class="neumorphic-inset p-3 text-center">
                            <div class="neumorphic-button w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                                <i class="fas fa-trophy text-xs" style="color: var(--success);"></i>
                            </div>
                            <div class="text-xs font-medium" style="color: var(--text-primary);">Consistent</div>
                            <div class="text-xs" style="color: var(--text-secondary);">80% this month</div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Insights -->
                <div class="neumorphic-card p-6">
                    <h3 class="text-lg font-semibold mb-3" style="color: var(--text-primary);">Weekly Insights</h3>
                    <div class="neumorphic-inset p-4 rounded-lg">
                        <div class="flex items-start space-x-3">
                            <div class="neumorphic-button w-8 h-8 flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-lightbulb text-xs" style="color: var(--wellness);"></i>
                            </div>
                            <div>
                                <p class="text-sm mb-2" style="color: var(--text-primary);">
                                    <strong>Great momentum!</strong> Your consistency improved by 12% this week.
                                </p>
                                <p class="text-xs" style="color: var(--text-secondary);">
                                    Tuesday and Thursday showed your strongest performance. Consider what made those days successful.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Home</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-line text-lg" style="color: var(--info);"></i>
                        <span class="text-xs mt-1" style="color: var(--info);">Progress</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-cog text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Settings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        const tabButtons = document.querySelectorAll('.tab-button-new');
        const chartLabels = document.querySelector('.chart-labels-new');
        
        const labelSets = {
            week: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            month: ['W1', 'W2', 'W3', 'W4'],
            year: ['2018', '2019', '2020', '2021', '2022', '2023', '2024']
        };
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                // Update chart labels based on selected period
                const period = button.dataset.period;
                const labels = labelSets[period];
                chartLabels.innerHTML = labels.map(label => `<span>${label}</span>`).join('');
            });
        });
        
        // Chart data for different periods
        const chartData = {
            week: [
                { labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], 
                  path: 'M 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75',
                  fill: 'M 20 140 L 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75 L 260 140 Z',
                  points: [{x: 20, y: 110}, {x: 60, y: 95}, {x: 100, y: 105}, {x: 140, y: 85}, {x: 180, y: 80}, {x: 220, y: 90}, {x: 260, y: 75}]
                },
                { labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], 
                  path: 'M 20 120 L 60 100 L 100 115 L 140 95 L 180 90 L 220 100 L 260 85',
                  fill: 'M 20 140 L 20 120 L 60 100 L 100 115 L 140 95 L 180 90 L 220 100 L 260 85 L 260 140 Z',
                  points: [{x: 20, y: 120}, {x: 60, y: 100}, {x: 100, y: 115}, {x: 140, y: 95}, {x: 180, y: 90}, {x: 220, y: 100}, {x: 260, y: 85}]
                },
                { labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], 
                  path: 'M 20 100 L 60 85 L 100 95 L 140 75 L 180 70 L 220 80 L 260 65',
                  fill: 'M 20 140 L 20 100 L 60 85 L 100 95 L 140 75 L 180 70 L 220 80 L 260 65 L 260 140 Z',
                  points: [{x: 20, y: 100}, {x: 60, y: 85}, {x: 100, y: 95}, {x: 140, y: 75}, {x: 180, y: 70}, {x: 220, y: 80}, {x: 260, y: 65}]
                }
            ],
            month: [
                { labels: ['W1', 'W2', 'W3', 'W4'], 
                  path: 'M 40 115 L 120 100 L 200 90 L 240 85',
                  fill: 'M 40 140 L 40 115 L 120 100 L 200 90 L 240 85 L 240 140 Z',
                  points: [{x: 40, y: 115}, {x: 120, y: 100}, {x: 200, y: 90}, {x: 240, y: 85}]
                },
                { labels: ['W1', 'W2', 'W3', 'W4'], 
                  path: 'M 40 125 L 120 110 L 200 100 L 240 95',
                  fill: 'M 40 140 L 40 125 L 120 110 L 200 100 L 240 95 L 240 140 Z',
                  points: [{x: 40, y: 125}, {x: 120, y: 110}, {x: 200, y: 100}, {x: 240, y: 95}]
                },
                { labels: ['W1', 'W2', 'W3', 'W4'], 
                  path: 'M 40 105 L 120 90 L 200 80 L 240 75',
                  fill: 'M 40 140 L 40 105 L 120 90 L 200 80 L 240 75 L 240 140 Z',
                  points: [{x: 40, y: 105}, {x: 120, y: 90}, {x: 200, y: 80}, {x: 240, y: 75}]
                }
            ],
            year: [
                { labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'], 
                  path: 'M 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75',
                  fill: 'M 20 140 L 20 110 L 60 95 L 100 105 L 140 85 L 180 80 L 220 90 L 260 75 L 260 140 Z',
                  points: [{x: 20, y: 110}, {x: 60, y: 95}, {x: 100, y: 105}, {x: 140, y: 85}, {x: 180, y: 80}, {x: 220, y: 90}, {x: 260, y: 75}]
                },
                { labels: ['2015', '2016', '2017', '2018', '2019', '2020', '2021'], 
                  path: 'M 20 130 L 60 125 L 100 120 L 140 110 L 180 105 L 220 100 L 260 95',
                  fill: 'M 20 140 L 20 130 L 60 125 L 100 120 L 140 110 L 180 105 L 220 100 L 260 95 L 260 140 Z',
                  points: [{x: 20, y: 130}, {x: 60, y: 125}, {x: 100, y: 120}, {x: 140, y: 110}, {x: 180, y: 105}, {x: 220, y: 100}, {x: 260, y: 95}]
                },
                { labels: ['2012', '2013', '2014', '2015', '2016', '2017', '2018'], 
                  path: 'M 20 135 L 60 130 L 100 128 L 140 125 L 180 120 L 220 115 L 260 110',
                  fill: 'M 20 140 L 20 135 L 60 130 L 100 128 L 140 125 L 180 120 L 220 115 L 260 110 L 260 140 Z',
                  points: [{x: 20, y: 135}, {x: 60, y: 130}, {x: 100, y: 128}, {x: 140, y: 125}, {x: 180, y: 120}, {x: 220, y: 115}, {x: 260, y: 110}]
                }
            ]
        };

        // Swipe functionality for chart navigation
        let currentChart = 0;
        let currentPeriod = 'year';
        const totalCharts = 3;
        const swipeDots = document.querySelectorAll('.swipe-dot-new');
        const chartContainer = document.querySelector('.chart-container-new');
        const chartSvg = document.querySelector('.chart-svg');
        
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        chartContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        chartContainer.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
            currentX = e.touches[0].clientX;
        });
        
        chartContainer.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            
            const deltaX = currentX - startX;
            const threshold = 50;
            
            if (deltaX > threshold && currentChart > 0) {
                // Swipe right - go to previous chart
                currentChart--;
                updateChart();
            } else if (deltaX < -threshold && currentChart < totalCharts - 1) {
                // Swipe left - go to next chart
                currentChart++;
                updateChart();
            }
            
            isDragging = false;
        });

        // Also add mouse events for desktop testing
        chartContainer.addEventListener('mousedown', (e) => {
            startX = e.clientX;
            isDragging = true;
        });
        
        chartContainer.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            currentX = e.clientX;
        });
        
        chartContainer.addEventListener('mouseup', (e) => {
            if (!isDragging) return;
            
            const deltaX = currentX - startX;
            const threshold = 50;
            
            if (deltaX > threshold && currentChart > 0) {
                currentChart--;
                updateChart();
            } else if (deltaX < -threshold && currentChart < totalCharts - 1) {
                currentChart++;
                updateChart();
            }
            
            isDragging = false;
        });
        
        function updateChart() {
            // Update swipe dots
            swipeDots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentChart);
            });
            
            // Get current chart data
            const data = chartData[currentPeriod][currentChart];
            
            // Update chart path
            const linePath = chartSvg.querySelector('.line-path-new');
            const fillPath = chartSvg.querySelector('.chart-fill-new');
            
            linePath.setAttribute('d', data.path);
            fillPath.setAttribute('d', data.fill);
            
            // Update data points
            const circles = chartSvg.querySelectorAll('circle');
            circles.forEach((circle, index) => {
                if (data.points[index]) {
                    circle.setAttribute('cx', data.points[index].x);
                    circle.setAttribute('cy', data.points[index].y);
                    circle.style.display = 'block';
                } else {
                    circle.style.display = 'none';
                }
            });
            
            // Update labels
            chartLabels.innerHTML = data.labels.map(label => `<span>${label}</span>`).join('');
        }
        
        // Update tab switching to reset chart position
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                // Update current period and reset chart position
                currentPeriod = button.dataset.period;
                currentChart = 0; // Reset to first chart when switching periods
                updateChart();
            });
        });
        
        // Initialize first chart
        updateChart();
    </script>
</body>
</html> 
</html> 