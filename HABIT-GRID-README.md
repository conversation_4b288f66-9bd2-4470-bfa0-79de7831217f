# Habit Grid Component

A reusable habit tracking grid component with GitHub-style visualization for habit tracking applications.

## Files

- `habit-grid.js` - Combined JavaScript and CSS (all-in-one file)
- `HABIT-GRID-README.md` - This documentation

> **Note:** The CSS styles are now embedded within the JavaScript file and automatically injected when the script loads.

## Features

- **GitHub-style visual grid** showing habit completion history
- **Responsive design** with horizontal scrolling
- **Touch-friendly** drag-to-scroll functionality
- **Multi-level visualization** (0-4 intensity levels)
- **Special indicators** for today's date and habit start date
- **Dark mode support** via CSS custom properties
- **Month labels** for easy navigation
- **Configurable options** for different use cases

## Quick Start

### 1. Include the file in your HTML

```html
<!DOCTYPE html>
<html>
<head>
    <!-- CSS custom properties (required) -->
    <style>
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
            }
        }
    </style>
</head>
<body>
    <!-- Container for the grid -->
    <div class="habit-grid-container" id="myHabitGrid"></div>
    
    <!-- Include the JavaScript (CSS auto-injected) -->
    <script src="habit-grid.js"></script>
</body>
</html>
```

### 2. Initialize and use the grid

```javascript
// Create a new grid instance
const habitGrid = new HabitGrid();

// Create sample habit data
const habit = {
    id: 1,
    name: "Morning Walk",
    timesPerDay: 1,
    startDaysAgo: 30,
    history: habitGrid.generateHabitHistory(15, 0.8, 30)
};

// Generate the grid
habitGrid.generateHabitGrid('myHabitGrid', habit);
```

## API Reference

### Constructor

```javascript
const habitGrid = new HabitGrid();
```

### Methods

#### `generateHabitHistory(streakDays, completionRate, startDaysAgo)`

Generates sample habit history data.

**Parameters:**
- `streakDays` (number): Current streak length
- `completionRate` (number): Overall completion rate (0-1)
- `startDaysAgo` (number): Days ago when habit started

**Returns:** Array of history objects

#### `generateHabitGrid(containerId, habit, options)`

Renders the habit grid in the specified container.

**Parameters:**
- `containerId` (string): ID of the container element
- `habit` (object): Habit object with history data
- `options` (object): Optional configuration
  - `showLabels` (boolean): Show day labels (default: true)
  - `autoScroll` (boolean): Auto-scroll to current week (default: true)

#### `updateHabitHistory(habit, completed)`

Updates habit history when toggling completion.

**Parameters:**
- `habit` (object): Habit object to update
- `completed` (boolean): New completion status

#### `createDemoGrid(containerId, weeks, completionRate)`

Creates a simple demo grid for previews.

**Parameters:**
- `containerId` (string): ID of container element
- `weeks` (number): Number of weeks to show (default: 4)
- `completionRate` (number): Completion rate (default: 0.7)

## Habit Object Structure

```javascript
const habit = {
    id: 1,                           // Unique identifier
    name: "Habit Name",              // Display name
    timesPerDay: 1,                  // How many times per day (1 for simple habits)
    startDaysAgo: 30,                // Days ago when habit started
    todayCount: 0,                   // Current count for today (for multi-check habits)
    history: [                       // Array of daily records
        {
            date: "2025-06-01",      // ISO date string
            level: 3,                // Completion level (0-4)
            completed: true,         // Boolean completion status
            isFuture: false          // Whether this date is in the future
        }
        // ... more history entries
    ]
};
```

## Grid Levels

The grid uses 5 different levels to represent habit completion:

- **Level 0**: No activity (neutral background)
- **Level 1**: Attempted but incomplete (concave styling)
- **Level 2**: Low progress (light green, slight convex)
- **Level 3**: Medium progress (medium green, convex)
- **Level 4**: Full completion (dark green, strong convex)

## CSS Custom Properties

The component requires these CSS custom properties:

```css
:root {
    --bg-primary: #e0e5ec;      /* Background color */
    --text-primary: #4a5568;    /* Primary text color */
    --text-secondary: #718096;  /* Secondary text color */
    --text-muted: #a0aec0;      /* Muted text color */
}
```

## Usage Examples

### Basic Grid

```javascript
const habitGrid = new HabitGrid();
const habit = {
    id: 1,
    name: "Daily Reading",
    timesPerDay: 1,
    startDaysAgo: 14,
    history: habitGrid.generateHabitHistory(10, 0.9, 14)
};

habitGrid.generateHabitGrid('readingGrid', habit);
```

### Multi-Check Habit

```javascript
const habit = {
    id: 2,
    name: "Drink Water",
    timesPerDay: 8,  // 8 glasses per day
    todayCount: 3,   // 3 completed today
    startDaysAgo: 7,
    history: habitGrid.generateHabitHistory(5, 0.7, 7)
};

habitGrid.generateHabitGrid('waterGrid', habit);
```

### Demo Grid (for onboarding)

```javascript
const habitGrid = new HabitGrid();
habitGrid.createDemoGrid('demoGrid', 8, 0.8); // 8 weeks, 80% completion
```

### Custom Options

```javascript
habitGrid.generateHabitGrid('customGrid', habit, {
    showLabels: false,  // Hide day labels
    autoScroll: false   // Don't auto-scroll to today
});
```

## Integration with Existing Code

### Updating Dashboard

To use the extracted component in `dashboard.html`, replace the existing grid functions:

```javascript
// Replace the existing generateHabitGrid function
const habitGrid = new HabitGrid();

function generateHabitGrid(habitId) {
    const habit = habits.find(h => h.id === habitId);
    if (habit) {
        habitGrid.generateHabitGrid(`habitGrid${habitId}`, habit);
    }
}

function updateHabitHistory(habitId, completed) {
    const habit = habits.find(h => h.id === habitId);
    if (habit) {
        habitGrid.updateHabitHistory(habit, completed);
        generateHabitGrid(habitId); // Regenerate grid
    }
}
```

### Browser Compatibility

- Modern browsers (Chrome 60+, Firefox 55+, Safari 12+)
- Touch events for mobile devices
- CSS Grid and Flexbox support required
- ES6 class syntax

## Customization

### Custom Date Function

Override the `getTodayDate()` method for testing:

```javascript
class CustomHabitGrid extends HabitGrid {
    getTodayDate() {
        return new Date(2025, 5, 28); // Custom date for testing
    }
}
```

### Custom Styling

Modify the CSS custom properties or override specific classes:

```css
.habit-grid-cell-4 {
    background: #your-custom-color !important;
}
```

## Performance Considerations

- Grid is optimized for up to 60 weeks of data
- Uses efficient DOM manipulation
- Lazy scroll initialization
- Touch events use passive listeners
- Minimal reflows during updates

## Troubleshooting

### Grid not appearing
- Ensure CSS custom properties are defined
- Check that container element exists
- Verify habit object has required properties

### Scrolling not working
- Check if `autoScroll` option is enabled
- Ensure container has proper overflow settings
- Verify touch events are not being prevented

### Wrong dates showing
- Check `getTodayDate()` method
- Verify habit `startDaysAgo` property
- Ensure history dates are in correct format 