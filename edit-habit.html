<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Edit Habit</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --error: #ff383c;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --error: #ff4245;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic {
            background: #E0E5EC;
            box-shadow: 8px 8px 16px #BDC2C9, -8px -8px 16px #FFFFFF;
            border-radius: 16px;
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic {
                background: #2C2C2C;
                box-shadow: 8px 8px 16px #1E1E1E, -8px -8px 16px #3A3A3A;
            }
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-inset {
            background: #E0E5EC;
            box-shadow: inset 4px 4px 8px #BDC2C9, inset -4px -4px 8px #FFFFFF;
            border-radius: 12px;
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-inset {
                background: #2C2C2C;
                box-shadow: inset 4px 4px 8px #1E1E1E, inset -4px -4px 8px #3A3A3A;
            }
        }

        .neumorphic-button {
            background: #E0E5EC;
            box-shadow: 4px 4px 8px #BDC2C9, -4px -4px 8px #FFFFFF;
            border-radius: 12px;
            transition: all 0.2s ease;
        }

        .neumorphic-button:active {
            box-shadow: inset 2px 2px 4px #BDC2C9, inset -2px -2px 4px #FFFFFF;
        }

        .neumorphic-button.selected {
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                background: #2C2C2C;
                box-shadow: 4px 4px 8px #1E1E1E, -4px -4px 8px #3A3A3A;
            }

            .neumorphic-button:active {
                box-shadow: inset 2px 2px 4px #1E1E1E, inset -2px -2px 4px #3A3A3A;
            }

            .neumorphic-button.selected {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        .category-colors {
            --health: #ff383c;
            --fitness: #ff8d28;
            --mindfulness: #cb30e0;
            --productivity: #0088ff;
            --wellness: #00c8b3;
            --learning: #6155f5;
            --nutrition: #34c759;
            --sleep: #af52de;
        }

        @media (prefers-color-scheme: dark) {
            .category-colors {
                --health: #ff4245;
                --fitness: #ff9230;
                --mindfulness: #db34f2;
                --productivity: #0091ff;
                --wellness: #00dac3;
                --learning: #7c6cff;
                --nutrition: #30d158;
                --sleep: #bf5af2;
            }
        }

        .health-color {
            color: var(--health);
        }

        .fitness-color {
            color: var(--fitness);
        }

        .mindfulness-color {
            color: var(--mindfulness);
        }

        .productivity-color {
            color: var(--productivity);
        }

        .wellness-color {
            color: var(--wellness);
        }

        .learning-color {
            color: var(--learning);
        }

        .nutrition-color {
            color: var(--nutrition);
        }

        .sleep-color {
            color: var(--sleep);
        }

        .text-input {
            background: transparent;
            border: none;
            outline: none;
            color: inherit;
            width: 100%;
            font-size: 16px;
            padding: 0;
        }

        .text-input::placeholder {
            color: #A8A8A8;
        }

        @media (prefers-color-scheme: dark) {
            .text-input::placeholder {
                color: #666;
            }
        }

        .time-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #007AFF;
            color: white;
            border-radius: 8px;
            padding: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            min-height: 48px;
        }

        .time-button:active {
            transform: scale(0.98);
        }

        .frequency-option {
            padding: 12px 16px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #E0E5EC;
            box-shadow: 2px 2px 4px #BDC2C9, -2px -2px 4px #FFFFFF;
        }

        .frequency-option.selected {
            background: #007AFF;
            color: white;
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        @media (prefers-color-scheme: dark) {
            .frequency-option {
                background: #2C2C2C;
                box-shadow: 2px 2px 4px #1E1E1E, -2px -2px 4px #3A3A3A;
            }

            .frequency-option.selected {
                background: #007AFF;
                color: white;
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
        }

        .category-option {
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #E0E5EC;
            box-shadow: 2px 2px 4px #BDC2C9, -2px -2px 4px #FFFFFF;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            min-height: 80px;
            justify-content: center;
        }

        .category-option.selected {
            background: var(--category-color);
            color: white;
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        .category-option.selected i {
            text-shadow: 0 0 0 white, 0 0 0 white, 1px 1px 0 white, -1px -1px 0 white, 1px -1px 0 white, -1px 1px 0 white;
            color: white !important;
        }

        @media (prefers-color-scheme: dark) {
            .category-option {
                background: #2C2C2C;
                box-shadow: 2px 2px 4px #1E1E1E, -2px -2px 4px #3A3A3A;
            }

            .category-option.selected {
                background: var(--category-color);
                color: white;
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
            }

            .category-option.selected i {
                text-shadow: 0 0 0 white, 0 0 0 white, 1px 1px 0 white, -1px -1px 0 white, 1px -1px 0 white, -1px 1px 0 white;
                color: white !important;
            }
        }

        .day-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #E0E5EC;
            box-shadow: 2px 2px 4px #BDC2C9, -2px -2px 4px #FFFFFF;
            font-size: 14px;
            font-weight: 500;
        }

        .day-option.selected {
            background: #007AFF;
            color: white;
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        @media (prefers-color-scheme: dark) {
            .day-option {
                background: #2C2C2C;
                box-shadow: 2px 2px 4px #1E1E1E, -2px -2px 4px #3A3A3A;
            }

            .day-option.selected {
                background: #007AFF;
                color: white;
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
        }

        .toggle-switch {
            width: 51px;
            height: 31px;
            background: #E0E5EC;
            border-radius: 16px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 2px 2px 4px #BDC2C9, inset -2px -2px 4px #FFFFFF;
        }

        .toggle-switch.active {
            background: #34C759;
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch .toggle-knob {
            width: 27px;
            height: 27px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active .toggle-knob {
            transform: translateX(20px);
        }

        @media (prefers-color-scheme: dark) {
            .toggle-switch {
                background: #2C2C2C;
                box-shadow: inset 2px 2px 4px #1E1E1E, inset -2px -2px 4px #3A3A3A;
            }

            .toggle-switch.active {
                background: #30D158;
            }

            .toggle-switch .toggle-knob {
                background: #FFFFFF;
            }
        }

        .create-button {
            background: #30d158;
            color: white;
            border-radius: 16px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(48, 209, 88, 0.4);
        }

        .create-button:active {
            box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2);
        }

        .create-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .plus-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .time-picker-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .time-picker-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .time-picker {
            background: #E0E5EC;
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            max-width: 90%;
            width: 300px;
        }

        @media (prefers-color-scheme: dark) {
            .time-picker {
                background: #2C2C2C;
            }
        }

        .time-slots {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin: 16px 0;
        }

        .time-slot {
            padding: 12px 8px;
            background: #E0E5EC;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 2px 2px 4px #BDC2C9, -2px -2px 4px #FFFFFF;
            font-size: 14px;
        }

        .time-slot:active {
            box-shadow: inset 2px 2px 4px #BDC2C9, inset -2px -2px 4px #FFFFFF;
        }

        @media (prefers-color-scheme: dark) {
            .time-slot {
                background: #3A3A3A;
                box-shadow: 2px 2px 4px #1E1E1E, -2px -2px 4px #484848;
            }

            .time-slot:active {
                box-shadow: inset 2px 2px 4px #1E1E1E, inset -2px -2px 4px #484848;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .back-button {
            background: rgba(120, 120, 128, 0.16);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-button:active {
            transform: scale(0.95);
        }

        @media (prefers-color-scheme: dark) {
            .back-button {
                background: rgba(120, 120, 128, 0.24);
            }
        }

        .icon-option.selected,
        .color-option.selected {
            box-shadow: inset 4px 4px 8px #BDC2C9, inset -4px -4px 8px #FFFFFF;
        }

        @media (prefers-color-scheme: dark) {
            .icon-option.selected,
            .color-option.selected {
                box-shadow: inset 4px 4px 8px #1E1E1E, inset -4px -4px 8px #3A3A3A;
            }
        }
    </style>
</head>

<body class="category-colors">
    <!-- Status Bar -->
    <div class="status-bar h-11 flex items-center justify-center text-sm font-medium">
        <div class="flex items-center gap-1">
            <div class="w-4 h-4 bg-black rounded-full opacity-80"></div>
            <div class="w-4 h-4 bg-black rounded-full opacity-60"></div>
            <div class="w-4 h-4 bg-black rounded-full opacity-40"></div>
        </div>
    </div>

    <div class="neumorphic-card">
        <div class="p-6 max-w-md mx-auto min-h-screen">
            <!-- Header -->
            <div class="flex items-center justify-between mb-8">
                                <div class="back-button" onclick="goBack()">
                    <i class="fas fa-arrow-left text-blue-500"></i>
                </div>
                <h1 class="text-xl font-bold">Edit Habit</h1>
                <div class="w-10"></div>
            </div>

        <!-- Main Content -->
        <div class="space-y-6">
            <!-- Habit Name -->
            <div class="neumorphic p-6 fade-in">
                <h3 class="text-lg font-semibold mb-4">Habit Name</h3>
                <div class="neumorphic-inset p-4">
                    <input type="text" id="habitInput" class="text-input" placeholder="Enter habit name" maxlength="50">
                </div>
                <div class="text-right text-xs text-gray-500 mt-2">
                    <span id="charCount">0</span>/50
                </div>
            </div>

            <!-- Category Selection -->
            <div class="neumorphic p-6 fade-in">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Category</h3>
                    <button class="neumorphic-button w-8 h-8 flex items-center justify-center" onclick="showCustomCategoryModal()" title="Add Custom Category">
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                </div>
                <div class="grid grid-cols-4 gap-3" id="categoryGrid">
                </div>
            </div>

            <!-- Times Per Day -->
            <div class="neumorphic p-6 fade-in">
                <h3 class="text-lg font-semibold mb-4">Times per Day</h3>
                <div class="flex items-center justify-between">
                    <button class="neumorphic-button w-10 h-10 flex items-center justify-center text-xl font-bold"
                        id="minusTimesBtn" onclick="changeTimesPerDay(-1)">
                        <span>&minus;</span>
                    </button>
                    <input type="text" id="timesPerDayInput" value="1" readonly
                        class="neumorphic-inset w-20 h-12 text-center text-2xl font-semibold mx-4"
                        style="cursor:pointer;" onclick="editTimesPerDay()" />
                    <button class="neumorphic-button w-10 h-10 flex items-center justify-center text-xl font-bold"
                        id="plusTimesBtn" onclick="changeTimesPerDay(1)">
                        <span>+</span>
                    </button>
                </div>
                <p class="text-xs mt-2 text-center opacity-60">How many times you want to do this habit per day</p>
            </div>

            <!-- Frequency -->
            <div class="neumorphic p-6 fade-in">
                <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Frequency</h3>
                <div class="grid grid-cols-3 gap-2 mb-4">
                    <button class="neumorphic-button p-3 text-center selected" data-frequency="daily"
                        onclick="selectFrequency('daily')">
                        <p class="text-sm font-medium" style="color: var(--text-primary);">Daily</p>
                    </button>
                    <button class="neumorphic-button p-3 text-center" data-frequency="weekdays"
                        onclick="selectFrequency('weekdays')">
                        <p class="text-sm font-medium" style="color: var(--text-primary);">Weekdays</p>
                    </button>
                    <button class="neumorphic-button p-3 text-center" data-frequency="custom"
                        onclick="selectFrequency('custom')">
                        <p class="text-sm font-medium" style="color: var(--text-primary);">Custom</p>
                    </button>
                </div>

                <!-- Custom Days Selection -->
                <div id="customDaysSection" style="display: none;" class="fade-in">
                    <p class="text-sm mb-3" style="color: var(--text-secondary);">Select days of the week:</p>
                    <div class="grid grid-cols-7 gap-1">
                        <button class="neumorphic-button p-2 text-center" data-day="monday"
                            onclick="toggleDay('monday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">M</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="tuesday"
                            onclick="toggleDay('tuesday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">T</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="wednesday"
                            onclick="toggleDay('wednesday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">W</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="thursday"
                            onclick="toggleDay('thursday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">T</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="friday"
                            onclick="toggleDay('friday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">F</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="saturday"
                            onclick="toggleDay('saturday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">S</p>
                        </button>
                        <button class="neumorphic-button p-2 text-center" data-day="sunday"
                            onclick="toggleDay('sunday')">
                            <p class="text-xs font-medium" style="color: var(--text-primary);">S</p>
                        </button>
                    </div>
                    <p class="text-xs mt-2" style="color: var(--text-muted);" id="selectedDaysText">No days selected</p>
                </div>
            </div>

            <!-- Reminder -->
            <div class="neumorphic p-6 fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Reminder</h3>
                    <div class="toggle-switch" id="reminderToggle">
                        <div class="toggle-knob"></div>
                    </div>
                </div>
                
                <div id="reminderTimeSection" style="display: none;">
                    <button class="neumorphic-button p-4 flex items-center justify-between w-full"
                        onclick="openTimePicker()">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-clock text-lg" style="color: var(--info);"></i>
                            <span style="color: var(--text-primary);" id="selectedTime">8:00 AM</span>
                        </div>
                        <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                    </button>
                    <p class="text-xs mt-2" style="color: var(--text-muted);">Tap to set custom reminder time</p>
                </div>

                <div id="reminderDisabledSection">
                    <div class="neumorphic-inset p-4 text-center">
                        <i class="fas fa-bell-slash text-2xl mb-2" style="color: var(--text-muted);"></i>
                        <p class="text-sm" style="color: var(--text-muted);">Reminders are disabled</p>
                    </div>
                </div>
            </div>

            <!-- Update Button -->
            <button class="create-button w-full" id="updateButton" disabled>
                <i class="fas fa-save mr-2"></i>
                <span>Update Habit</span>
            </button>
        </div>
    </div>

    <!-- Time Picker Overlay -->
    <div class="time-picker-overlay" id="timePickerOverlay">
        <div class="time-picker">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Select Time</h3>
                <button class="text-blue-500 font-medium" onclick="closeTimePicker()">Done</button>
            </div>
            <div class="time-slots" id="timeSlots">
                <!-- Time slots will be generated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Custom Category Modal -->
    <div class="time-picker-overlay" id="customCategoryModal">
        <div class="time-picker">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Create Custom Category</h3>
                <button class="text-blue-500 font-medium" onclick="hideCustomCategoryModal()">Cancel</button>
            </div>

            <!-- Category Name Input -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Category Name:</label>
                <input type="text" id="customCategoryNameInput" class="neumorphic-inset w-full p-3 text-base"
                    placeholder="Enter category name" maxlength="20" />
            </div>

            <!-- Icon Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Choose Icon:</label>
                <div class="grid grid-cols-6 gap-2 mb-3" id="iconGrid">
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-star" onclick="selectIcon('fas fa-star')">
                        <i class="fas fa-star text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-trophy" onclick="selectIcon('fas fa-trophy')">
                        <i class="fas fa-trophy text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-target" onclick="selectIcon('fas fa-target')">
                        <i class="fas fa-target text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-lightbulb" onclick="selectIcon('fas fa-lightbulb')">
                        <i class="fas fa-lightbulb text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-paint-brush" onclick="selectIcon('fas fa-paint-brush')">
                        <i class="fas fa-paint-brush text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-camera" onclick="selectIcon('fas fa-camera')">
                        <i class="fas fa-camera text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-music" onclick="selectIcon('fas fa-music')">
                        <i class="fas fa-music text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-gamepad" onclick="selectIcon('fas fa-gamepad')">
                        <i class="fas fa-gamepad text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-home" onclick="selectIcon('fas fa-home')">
                        <i class="fas fa-home text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-car" onclick="selectIcon('fas fa-car')">
                        <i class="fas fa-car text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-shopping-cart" onclick="selectIcon('fas fa-shopping-cart')">
                        <i class="fas fa-shopping-cart text-base"></i>
                    </button>
                    <button class="neumorphic-button p-2 text-center icon-option" data-icon="fas fa-tools" onclick="selectIcon('fas fa-tools')">
                        <i class="fas fa-tools text-base"></i>
                    </button>
                </div>
            </div>

            <!-- Color Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Choose Color:</label>
                <div class="grid grid-cols-6 gap-2 mb-3" id="colorGrid">
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#ff383c" onclick="selectColor('#ff383c')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #ff383c;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#ff8d28" onclick="selectColor('#ff8d28')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #ff8d28;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#34c759" onclick="selectColor('#34c759')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #34c759;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#0088ff" onclick="selectColor('#0088ff')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #0088ff;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#cb30e0" onclick="selectColor('#cb30e0')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #cb30e0;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#00c8b3" onclick="selectColor('#00c8b3')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #00c8b3;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#6155f5" onclick="selectColor('#6155f5')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #6155f5;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#af52de" onclick="selectColor('#af52de')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #af52de;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#8e8e93" onclick="selectColor('#8e8e93')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #8e8e93;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#48484a" onclick="selectColor('#48484a')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #48484a;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#32d74b" onclick="selectColor('#32d74b')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #32d74b;"></div>
                    </button>
                    <button class="neumorphic-button p-2 text-center color-option" data-color="#007aff" onclick="selectColor('#007aff')">
                        <div class="w-5 h-5 rounded-full mx-auto" style="background-color: #007aff;"></div>
                    </button>
                </div>
            </div>

            <!-- Create Button -->
            <button class="create-button w-full" id="createCategoryButton" onclick="createCustomCategory()" disabled>
                <i class="fas fa-plus mr-2"></i>
                <span>Create Category</span>
            </button>
        </div>
    </div>
    </div>

    <script>
        let selectedCategory = '';
        let selectedFrequency = 'daily';
        let customDays = [];
        let reminderEnabled = false;
        let selectedTime = '8:00 AM';
        let timesPerDay = 1;
        let habitToEdit = null;
        let editHabitId = null;

        // Custom category state
        let selectedIcon = 'fas fa-star';
        let selectedColor = '#ff383c';
        let customCategories = [];

        // Load custom categories from localStorage
        function loadCustomCategories() {
            try {
                const saved = localStorage.getItem('habitTracker_customCategories');
                customCategories = saved ? JSON.parse(saved) : [];
            } catch (error) {
                console.error('Error loading custom categories:', error);
                customCategories = [];
            }
        }

        // Save custom categories to localStorage
        function saveCustomCategories() {
            try {
                localStorage.setItem('habitTracker_customCategories', JSON.stringify(customCategories));
            } catch (error) {
                console.error('Error saving custom categories:', error);
            }
        }

        // Render category grid with default and custom categories
        function renderCategoryGrid() {
            const categoryGrid = document.getElementById('categoryGrid');
            
            // Default categories
            const defaultCategories = [
                { id: 'health', name: 'Health', icon: 'fas fa-heart', colorVar: 'var(--health)' },
                { id: 'fitness', name: 'Fitness', icon: 'fas fa-dumbbell', colorVar: 'var(--fitness)' },
                { id: 'mindfulness', name: 'Mind', icon: 'fas fa-brain', colorVar: 'var(--mindfulness)' },
                { id: 'productivity', name: 'Work', icon: 'fas fa-laptop', colorVar: 'var(--productivity)' },
                { id: 'wellness', name: 'Wellness', icon: 'fas fa-spa', colorVar: 'var(--wellness)' },
                { id: 'learning', name: 'Learning', icon: 'fas fa-book', colorVar: 'var(--learning)' },
                { id: 'nutrition', name: 'Nutrition', icon: 'fas fa-apple-alt', colorVar: 'var(--nutrition)' },
                { id: 'sleep', name: 'Sleep', icon: 'fas fa-bed', colorVar: 'var(--sleep)' }
            ];

            let html = '';

            // Add default categories
            defaultCategories.forEach(cat => {
                html += `
                    <div class="category-option" data-category="${cat.id}" style="--category-color: ${cat.colorVar};" onclick="selectCategory('${cat.id}')">
                        <i class="${cat.icon} text-xl" style="color: ${cat.colorVar};"></i>
                        <span class="text-xs font-medium">${cat.name}</span>
                    </div>
                `;
            });

            // Add custom categories
            customCategories.forEach(cat => {
                html += `
                    <div class="category-option" data-category="${cat.id}" style="--category-color: ${cat.color};" onclick="selectCategory('${cat.id}')">
                        <i class="${cat.icon} text-xl" style="color: ${cat.color};"></i>
                        <span class="text-xs font-medium">${cat.name}</span>
                    </div>
                `;
            });

            categoryGrid.innerHTML = html;
        }

        // Custom category modal functions
        function showCustomCategoryModal() {
            document.getElementById('customCategoryModal').classList.add('active');
            // Reset form
            document.getElementById('customCategoryNameInput').value = '';
            selectedIcon = 'fas fa-star';
            selectedColor = '#ff383c';
            updateIconSelection();
            updateColorSelection();
            validateCustomCategoryForm();
        }

        function hideCustomCategoryModal() {
            document.getElementById('customCategoryModal').classList.remove('active');
        }

        function selectIcon(icon) {
            selectedIcon = icon;
            updateIconSelection();
            validateCustomCategoryForm();
        }

        function selectColor(color) {
            selectedColor = color;
            updateColorSelection();
            validateCustomCategoryForm();
        }

        function updateIconSelection() {
            document.querySelectorAll('.icon-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            const iconBtn = document.querySelector(`[data-icon="${selectedIcon}"]`);
            if (iconBtn) iconBtn.classList.add('selected');
        }

        function updateColorSelection() {
            document.querySelectorAll('.color-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            const colorBtn = document.querySelector(`[data-color="${selectedColor}"]`);
            if (colorBtn) colorBtn.classList.add('selected');
        }

        function validateCustomCategoryForm() {
            const nameInput = document.getElementById('customCategoryNameInput');
            const createButton = document.getElementById('createCategoryButton');
            
            const name = nameInput.value.trim();
            const isValid = name.length > 0 && selectedIcon && selectedColor;
            
            createButton.disabled = !isValid;
        }

        function createCustomCategory() {
            const nameInput = document.getElementById('customCategoryNameInput');
            const name = nameInput.value.trim();
            
            if (!name || !selectedIcon || !selectedColor) {
                alert('Please fill in all fields');
                return;
            }

            // Check if category name already exists
            const existingCategory = customCategories.find(cat => cat.name.toLowerCase() === name.toLowerCase());
            if (existingCategory) {
                alert('A category with this name already exists');
                return;
            }

            // Create new category
            const newCategory = {
                id: 'custom_' + Date.now(),
                name: name,
                icon: selectedIcon,
                color: selectedColor
            };

            customCategories.push(newCategory);
            saveCustomCategories();
            renderCategoryGrid();
            hideCustomCategoryModal();

            // Auto-select the new category
            selectCategory(newCategory.id);
        }

        // Add event listener for custom category name input
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('customCategoryNameInput');
            if (nameInput) {
                nameInput.addEventListener('input', validateCustomCategoryForm);
            }
        });

        document.addEventListener('DOMContentLoaded', function () {
            // Load custom categories and render grid
            loadCustomCategories();
            renderCategoryGrid();
            
            // Get habit ID from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            editHabitId = urlParams.get('id');
            
            if (!editHabitId) {
                alert('No habit selected for editing');
                window.location.href = 'dashboard.html';
                return;
            }

            // Debug: Show what's in localStorage
            const rawStorage = localStorage.getItem('habitTracker_habits');
            console.log('Raw localStorage data:', rawStorage);

            // Load habit data
            loadHabitData();
            
            // Initialize event listeners
            initializeEventListeners();
            generateTimeSlots();
            validateForm();
        });

        function loadHabitData() {
            try {
                const allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                habitToEdit = allHabits.find(h => h.id === parseInt(editHabitId));
                
                if (!habitToEdit) {
                    alert('Habit not found');
                    window.location.href = 'dashboard.html';
                    return;
                }

                // Pre-populate form fields
                document.getElementById('habitInput').value = habitToEdit.name || '';
                document.getElementById('timesPerDayInput').value = habitToEdit.timesPerDay || 1;
                
                selectedCategory = habitToEdit.category || '';
                selectedFrequency = habitToEdit.frequency || 'daily';
                selectedTime = habitToEdit.reminderTime || '8:00 AM';
                customDays = habitToEdit.customDays || [];
                reminderEnabled = habitToEdit.reminderEnabled !== false; // Default to true if not explicitly false
                timesPerDay = habitToEdit.timesPerDay || 1;

                console.log('Loaded habit data:', {
                    name: habitToEdit.name,
                    category: selectedCategory,
                    frequency: selectedFrequency,
                    timesPerDay: timesPerDay,
                    reminderEnabled: reminderEnabled,
                    reminderTime: selectedTime,
                    customDays: customDays
                });

                console.log('Raw habit object:', habitToEdit);

                // Use setTimeout to ensure DOM is ready for UI updates
                setTimeout(() => {                // Update UI to reflect loaded data with setTimeout to ensure DOM is ready
                setTimeout(() => {
                    console.log('Updating UI components...');
                    updateCategorySelection();
                    updateFrequencySelection();
                    updateReminderToggle();
                    updateTimeDisplay();
                    updateCustomDays();
                    validateForm();
                    console.log('UI update complete');
                }, 100);
                }, 100);
                
            } catch (error) {
                console.error('Error loading habit data:', error);
                alert('Error loading habit data');
                window.location.href = 'dashboard.html';
            }
        }

        function updateCategorySelection() {
            document.querySelectorAll('.category-option').forEach(option => {
                if (option.dataset.category === selectedCategory) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        function updateFrequencySelection() {
            console.log('updateFrequencySelection called with selectedFrequency:', selectedFrequency);
            
            // Update frequency buttons selection
            document.querySelectorAll('[data-frequency]').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-frequency="${selectedFrequency}"]`).classList.add('selected');

            // Show/hide custom days section
            const customDaysSection = document.getElementById('customDaysSection');
            if (selectedFrequency === 'custom') {
                customDaysSection.style.display = 'block';
                // Pre-select weekdays as default for custom if no days selected
                if (customDays.length === 0) {
                    ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'].forEach(day => {
                        toggleDay(day);
                    });
                }
            } else {
                customDaysSection.style.display = 'none';
                // Clear custom days when switching away from custom
                customDays = [];
                document.querySelectorAll('[data-day]').forEach(btn => {
                    btn.classList.remove('selected');
                });
                updateSelectedDaysText();
            }
        }

        function updateReminderToggle() {
            const toggle = document.getElementById('reminderToggle');
            const timeSection = document.getElementById('reminderTimeSection');
            const disabledSection = document.getElementById('reminderDisabledSection');
            
            if (reminderEnabled) {
                toggle.classList.add('active');
                timeSection.style.display = 'block';
                disabledSection.style.display = 'none';
            } else {
                toggle.classList.remove('active');
                timeSection.style.display = 'none';
                disabledSection.style.display = 'block';
            }
        }

        function updateTimeDisplay() {
            document.getElementById('selectedTime').textContent = selectedTime;
        }

        function updateCustomDays() {
            console.log('updateCustomDays called with customDays:', customDays, 'selectedFrequency:', selectedFrequency);
            
            // Update day button selections
            document.querySelectorAll('[data-day]').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            customDays.forEach(day => {
                const dayButton = document.querySelector(`[data-day="${day}"]`);
                if (dayButton) {
                    dayButton.classList.add('selected');
                }
            });
            
            updateSelectedDaysText();
        }

        function initializeEventListeners() {
            // Habit name input
            const habitInput = document.getElementById('habitInput');
            const charCount = document.getElementById('charCount');
            
            habitInput.addEventListener('input', function () {
                const length = this.value.length;
                charCount.textContent = length;
                validateForm();
            });

            // Times per day input is now readonly and handled by buttons

            // Category selection
            document.querySelectorAll('.category-option').forEach(option => {
                option.addEventListener('click', function () {
                    selectCategory(this.dataset.category);
                });
            });

            // Frequency selection and custom days now use inline onclick handlers

            // Reminder toggle
            document.getElementById('reminderToggle').addEventListener('click', toggleReminder);

            // Time picker - Updated to match new button structure
            // The click handler is now inline in the HTML: onclick="openTimePicker()"

            // Update button
            document.getElementById('updateButton').addEventListener('click', updateHabit);
        }

        function selectCategory(category) {
            selectedCategory = category;
            updateCategorySelection();
            validateForm();
        }

        function selectFrequency(frequency) {
            selectedFrequency = frequency;
            updateFrequencySelection();
            validateForm();
        }

        function toggleDay(day) {
            const dayButton = document.querySelector(`[data-day="${day}"]`);

            if (customDays.includes(day)) {
                // Remove day
                customDays = customDays.filter(d => d !== day);
                dayButton.classList.remove('selected');
            } else {
                // Add day
                customDays.push(day);
                dayButton.classList.add('selected');
            }

            updateSelectedDaysText();
            validateForm();
        }

        // Update selected days text
        function updateSelectedDaysText() {
            const selectedDaysText = document.getElementById('selectedDaysText');
            const dayNames = {
                monday: 'Mon',
                tuesday: 'Tue',
                wednesday: 'Wed',
                thursday: 'Thu',
                friday: 'Fri',
                saturday: 'Sat',
                sunday: 'Sun'
            };

            if (customDays.length === 0) {
                selectedDaysText.textContent = 'No days selected';
                selectedDaysText.style.color = 'var(--error)';
            } else if (customDays.length === 7) {
                selectedDaysText.textContent = 'Every day';
                selectedDaysText.style.color = 'var(--text-muted)';
            } else {
                const daysList = customDays.map(day => dayNames[day]).join(', ');
                selectedDaysText.textContent = `${customDays.length} days: ${daysList}`;
                selectedDaysText.style.color = 'var(--text-muted)';
            }
        }

        function toggleReminder() {
            reminderEnabled = !reminderEnabled;
            const toggle = document.getElementById('reminderToggle');
            const timeSection = document.getElementById('reminderTimeSection');
            const disabledSection = document.getElementById('reminderDisabledSection');

            if (reminderEnabled) {
                toggle.classList.add('active');
                timeSection.style.display = 'block';
                disabledSection.style.display = 'none';
            } else {
                toggle.classList.remove('active');
                timeSection.style.display = 'none';
                disabledSection.style.display = 'block';
            }
        }

        function openTimePicker() {
            document.getElementById('timePickerOverlay').classList.add('active');
        }

        function closeTimePicker() {
            document.getElementById('timePickerOverlay').classList.remove('active');
        }

        function selectTime(time) {
            selectedTime = time;
            updateTimeDisplay();
            closeTimePicker();
        }

        function generateTimeSlots() {
            const timeSlots = document.getElementById('timeSlots');
            const times = [
                '6:00 AM', '7:00 AM', '8:00 AM', '9:00 AM', '10:00 AM', '11:00 AM',
                '12:00 PM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM',
                '6:00 PM', '7:00 PM', '8:00 PM', '9:00 PM', '10:00 PM', '11:00 PM'
            ];

            times.forEach(time => {
                const slot = document.createElement('div');
                slot.className = 'time-slot';
                slot.textContent = time;
                slot.onclick = () => selectTime(time);
                timeSlots.appendChild(slot);
            });
        }

        // Times per day change handler
        function changeTimesPerDay(delta) {
            timesPerDay = Math.max(1, Math.min(10, timesPerDay + delta));
            document.getElementById('timesPerDayInput').value = timesPerDay;
            validateForm();
        }

        // Edit times per day field
        function editTimesPerDay() {
            const newValue = prompt('Enter times per day (minimum 1):', timesPerDay);
            const intValue = parseInt(newValue, 10);
            if (!isNaN(intValue) && intValue >= 1 && intValue <= 10) {
                timesPerDay = intValue;
                document.getElementById('timesPerDayInput').value = timesPerDay;
                validateForm();
            }
        }

        function validateForm() {
            const habitName = document.getElementById('habitInput').value.trim();
            const timesValid = timesPerDay >= 1 && timesPerDay <= 10;
            const categorySelected = selectedCategory !== '';
            const frequencyValid = selectedFrequency === 'custom' ? customDays.length > 0 : true;

            const isValid = habitName && categorySelected && timesValid && frequencyValid;
            
            const updateButton = document.getElementById('updateButton');
            updateButton.disabled = !isValid;
        }

        function updateHabit() {
            if (!habitToEdit) return;

            const habitName = document.getElementById('habitInput').value.trim();
            
            // Update habit object
            habitToEdit.name = habitName;
            habitToEdit.category = selectedCategory;
            habitToEdit.frequency = selectedFrequency;
            habitToEdit.customDays = customDays;
            habitToEdit.reminderEnabled = reminderEnabled;
            habitToEdit.reminderTime = selectedTime;
            habitToEdit.timesPerDay = timesPerDay;

            try {
                // Get all habits from localStorage
                let allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                
                // Find and update the habit
                const habitIndex = allHabits.findIndex(h => h.id === habitToEdit.id);
                if (habitIndex >= 0) {
                    allHabits[habitIndex] = habitToEdit;
                } else {
                    // If not found, add it (shouldn't happen, but safety)
                    allHabits.push(habitToEdit);
                }

                // Save back to localStorage
                localStorage.setItem('habitTracker_habits', JSON.stringify(allHabits));

                // Show success message and redirect
                alert('Habit updated successfully!');
                window.location.href = 'dashboard.html';

            } catch (error) {
                console.error('Error updating habit:', error);
                alert('Error updating habit. Please try again.');
            }
        }

        // Navigation functions
        function goBack() {
            window.location.href = 'dashboard.html';
        }

        // Close time picker when clicking outside
        document.getElementById('timePickerOverlay').addEventListener('click', function (e) {
            if (e.target === this) {
                closeTimePicker();
            }
        });
    </script>
</body>

</html>
