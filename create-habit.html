<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Create Habit</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --error: #ff383c;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --error: #ff4245;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: none;
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button:active,
        .neumorphic-button.selected {
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {

            .neumorphic-button:active,
            .neumorphic-button.selected {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-inset {
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: inset 6px 6px 10px rgba(163, 177, 198, 0.6), inset -6px -6px 10px rgba(255, 255, 255, 0.5);
            border: none;
            outline: none;
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-inset {
                box-shadow: inset 6px 6px 10px rgba(0, 0, 0, 0.8), inset -6px -6px 10px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-inset:focus {
            box-shadow: inset 4px 4px 8px rgba(163, 177, 198, 0.8), inset -4px -4px 8px rgba(255, 255, 255, 0.7);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-inset:focus {
                box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.9), inset -4px -4px 8px rgba(51, 51, 51, 0.4);
            }
        }

        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        .category-colors {
            --health: #ff383c;
            --fitness: #ff8d28;
            --mindfulness: #cb30e0;
            --productivity: #0088ff;
            --wellness: #00c8b3;
            --learning: #6155f5;
            --nutrition: #34c759;
            --sleep: #af52de;
        }

        @media (prefers-color-scheme: dark) {
            .category-colors {
                --health: #ff4245;
                --fitness: #ff9230;
                --mindfulness: #db34f2;
                --productivity: #0091ff;
                --wellness: #00dac3;
                --learning: #7c6cff;
                --nutrition: #30d158;
                --sleep: #bf5af2;
            }
        }

        .health-color {
            color: var(--health);
        }

        .fitness-color {
            color: var(--fitness);
        }

        .mindfulness-color {
            color: var(--mindfulness);
        }

        .productivity-color {
            color: var(--productivity);
        }

        .wellness-color {
            color: var(--wellness);
        }

        .learning-color {
            color: var(--learning);
        }

        .nutrition-color {
            color: var(--nutrition);
        }

        .sleep-color {
            color: var(--sleep);
        }

        .create-button {
            background: #30d158;
            color: white;
            box-shadow: 0 4px 15px rgba(48, 209, 88, 0.4);
        }

        .create-button:active {
            box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2);
        }

        .time-picker {
            display: none;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border-radius: 20px 20px 0 0;
            z-index: 50;
            animation: slideUp 0.3s ease-out;
            max-height: 60vh;
            overflow-y: auto;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            -webkit-overflow-scrolling: touch;
            margin-bottom: 0;
            padding-bottom: env(safe-area-inset-bottom);
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
            }

            to {
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-state {
            box-shadow: inset 6px 6px 10px rgba(255, 56, 60, 0.3), inset -6px -6px 10px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .error-state {
                box-shadow: inset 6px 6px 10px rgba(255, 66, 69, 0.3), inset -6px -6px 10px rgba(51, 51, 51, 0.3);
            }
        }

        /* Toggle Switch Styles */
        .toggle-switch {
            width: 48px;
            height: 24px;
            background: var(--bg-primary);
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        .toggle-knob {
            width: 20px;
            height: 20px;
            background: var(--bg-primary);
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
        }

        .toggle-switch.active {
            background: linear-gradient(145deg, var(--success), var(--wellness));
            box-shadow: inset 2px 2px 4px rgba(52, 199, 89, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
        }

        .toggle-switch.active .toggle-knob {
            left: 26px;
            background: white;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }

        @media (prefers-color-scheme: dark) {
            .toggle-switch {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }

            .toggle-knob {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
            }

            .toggle-switch.active {
                background: linear-gradient(145deg, var(--success), var(--wellness));
                box-shadow: inset 2px 2px 4px rgba(48, 209, 88, 0.4), inset -2px -2px 4px rgba(51, 51, 51, 0.2);
            }
        }
    </style>
</head>

<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden category-colors" style="background-color: var(--bg-primary);">

            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 py-6 min-h-screen pb-32">

                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <button class="neumorphic-button w-10 h-10 flex items-center justify-center" onclick="goBack()">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </button>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">New Habit</h1>
                        <p class="text-xs" style="color: var(--text-secondary);">Create your mini habit</p>
                    </div>
                    <button class="neumorphic-button w-10 h-10 flex items-center justify-center" onclick="showHelp()">
                        <i class="fas fa-question-circle text-sm" style="color: var(--text-primary);"></i>
                    </button>
                </div>

                <!-- Philosophy Reminder -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                            <i class="fas fa-feather text-sm" style="color: var(--wellness);"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold" style="color: var(--text-primary);">Mini Habits Philosophy</h3>
                            <p class="text-sm" style="color: var(--text-secondary);">Too small to fail</p>
                        </div>
                    </div>
                    <p class="text-sm" style="color: var(--text-secondary);">Start with the smallest possible version.
                        You can always do more, but the goal is consistency!</p>
                </div>

                <!-- Category Selection -->
                <div class="neumorphic-card p-5 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Choose Category</h3>
                        <button class="neumorphic-button w-8 h-8 flex items-center justify-center" onclick="showCustomCategoryModal()" title="Add Custom Category">
                            <i class="fas fa-plus text-sm" style="color: var(--text-primary);"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-2" id="categoryGrid">
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="health"
                            onclick="selectCategory('health')">
                            <i class="fas fa-heart text-base health-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Health</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="fitness"
                            onclick="selectCategory('fitness')">
                            <i class="fas fa-dumbbell text-base fitness-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Fitness</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="mindfulness"
                            onclick="selectCategory('mindfulness')">
                            <i class="fas fa-brain text-base mindfulness-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Mindfulness</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="productivity"
                            onclick="selectCategory('productivity')">
                            <i class="fas fa-tasks text-base productivity-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Productivity</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="wellness"
                            onclick="selectCategory('wellness')">
                            <i class="fas fa-spa text-base wellness-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Wellness</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="learning"
                            onclick="selectCategory('learning')">
                            <i class="fas fa-book text-base learning-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Learning</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="nutrition"
                            onclick="selectCategory('nutrition')">
                            <i class="fas fa-apple-alt text-base nutrition-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Nutrition</span>
                        </button>
                        <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="sleep"
                            onclick="selectCategory('sleep')">
                            <i class="fas fa-moon text-base sleep-color"></i>
                            <span class="text-xs font-medium" style="color: var(--text-primary);">Sleep</span>
                        </button>
                    </div>
                </div>

                <!-- Habit Name -->
                <div class="neumorphic-card p-5 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Habit Name</h3>
                    <input type="text" id="habitInput" placeholder="e.g., 1 push-up, 1 page, 1 minute walk"
                        class="neumorphic-inset w-full p-4 text-base" style="color: var(--text-primary);"
                        oninput="validateInput()" />
                    <p class="text-xs mt-2" style="color: var(--text-muted);" id="inputHint">Select a category to see
                        suggestions</p>
                </div>

                <!-- Quick Examples -->
                <div class="neumorphic-card p-5 mb-6 fade-in" id="examplesSection" style="display: none;">
                    <h3 class="text-base font-semibold mb-3" style="color: var(--text-primary);" id="examplesTitle">
                        Examples</h3>
                    <div class="space-y-2" id="examplesList">
                        <!-- Dynamic examples will be inserted here -->
                    </div>
                </div>


                <!-- Times Per Day Section -->
                <div class="neumorphic-card p-5 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Times per Day</h3>
                    <div class="flex items-center justify-between">
                        <button class="neumorphic-button w-10 h-10 flex items-center justify-center text-xl font-bold"
                            id="minusTimesBtn" onclick="changeTimesPerDay(-1)">
                            <span style="color: var(--text-primary);">&minus;</span>
                        </button>
                        <input type="text" id="timesPerDayInput" value="1" readonly
                            class="neumorphic-inset w-20 h-12 text-center text-2xl font-semibold mx-4"
                            style="color: var(--text-primary); cursor:pointer;" onclick="editTimesPerDay()" />
                        <button class="neumorphic-button w-10 h-10 flex items-center justify-center text-xl font-bold"
                            id="plusTimesBtn" onclick="changeTimesPerDay(1)">
                            <span style="color: var(--text-primary);">+</span>
                        </button>
                    </div>
                    <p class="text-xs mt-2 text-center" style="color: var(--text-muted);">How many times you want to do
                        this habit per day</p>
                </div>

                <!-- Frequency (moved here below Times Per Day, above Reminder Time) -->
                <div class="neumorphic-card p-5 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Frequency</h3>
                    <div class="grid grid-cols-3 gap-2 mb-4">
                        <button class="neumorphic-button p-3 text-center selected" data-frequency="daily"
                            onclick="selectFrequency('daily')">
                            <p class="text-sm font-medium" style="color: var(--text-primary);">Daily</p>
                        </button>
                        <button class="neumorphic-button p-3 text-center" data-frequency="weekdays"
                            onclick="selectFrequency('weekdays')">
                            <p class="text-sm font-medium" style="color: var(--text-primary);">Weekdays</p>
                        </button>
                        <button class="neumorphic-button p-3 text-center" data-frequency="custom"
                            onclick="selectFrequency('custom')">
                            <p class="text-sm font-medium" style="color: var(--text-primary);">Custom</p>
                        </button>
                    </div>

                    <!-- Custom Days Selection -->
                    <div id="customDaysSection" style="display: none;" class="fade-in">
                        <p class="text-sm mb-3" style="color: var(--text-secondary);">Select days of the week:</p>
                        <div class="grid grid-cols-7 gap-1">
                            <button class="neumorphic-button p-2 text-center" data-day="monday"
                                onclick="toggleDay('monday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">M</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="tuesday"
                                onclick="toggleDay('tuesday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">T</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="wednesday"
                                onclick="toggleDay('wednesday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">W</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="thursday"
                                onclick="toggleDay('thursday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">T</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="friday"
                                onclick="toggleDay('friday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">F</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="saturday"
                                onclick="toggleDay('saturday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">S</p>
                            </button>
                            <button class="neumorphic-button p-2 text-center" data-day="sunday"
                                onclick="toggleDay('sunday')">
                                <p class="text-xs font-medium" style="color: var(--text-primary);">S</p>
                            </button>
                        </div>
                        <p class="text-xs mt-2" style="color: var(--text-muted);" id="selectedDaysText">No days selected
                        </p>
                    </div>
                </div>

                <!-- Frequency section removed: duplicate at bottom deleted, only the first remains. -->

                <!-- Reminder Time -->
                <div class="neumorphic-card p-5 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Reminder Time</h3>
                        <div class="toggle-switch" id="reminderToggle" onclick="toggleReminder()">
                            <div class="toggle-knob"></div>
                        </div>
                    </div>

                    <div id="reminderTimeSection" style="display: none;">
                        <button class="neumorphic-button p-4 flex items-center justify-between w-full"
                            onclick="showTimePicker()">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-lg" style="color: var(--info);"></i>
                                <span style="color: var(--text-primary);" id="selectedTime">8:00 AM</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </button>
                        <p class="text-xs mt-2" style="color: var(--text-muted);">Tap to set custom reminder time</p>
                    </div>

                    <div id="reminderDisabledSection">
                        <div class="neumorphic-inset p-4 text-center">
                            <i class="fas fa-bell-slash text-2xl mb-2" style="color: var(--text-muted);"></i>
                            <p class="text-sm" style="color: var(--text-muted);">Reminders are disabled</p>
                        </div>
                    </div>
                </div>

                <!-- Frequency section removed: duplicate deleted, only the moved one remains. -->

                <!-- Success Tips -->
                <div class="neumorphic-card p-5 mb-6">
                    <h3 class="text-base font-semibold mb-3" style="color: var(--text-primary);">Success Tips</h3>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="neumorphic-button w-6 h-6 flex items-center justify-center mt-0.5">
                                <i class="fas fa-check text-xs" style="color: var(--success);"></i>
                            </div>
                            <p class="text-sm" style="color: var(--text-secondary);">Start ridiculously small - you can
                                always do more</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="neumorphic-button w-6 h-6 flex items-center justify-center mt-0.5">
                                <i class="fas fa-link text-xs" style="color: var(--info);"></i>
                            </div>
                            <p class="text-sm" style="color: var(--text-secondary);">Link to an existing habit for
                                better consistency</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="neumorphic-button w-6 h-6 flex items-center justify-center mt-0.5">
                                <i class="fas fa-trophy text-xs" style="color: var(--warning);"></i>
                            </div>
                            <p class="text-sm" style="color: var(--text-secondary);">Celebrate completion - even small
                                wins matter</p>
                        </div>
                    </div>
                </div>

                <!-- Create Button -->
                <button id="createButton"
                    class="neumorphic-button create-button w-full flex items-center justify-center p-3 text-base font-semibold rounded-2xl mb-6 transition-all duration-200"
                    onclick="createHabit()" disabled style="opacity: 0.5; min-height:44px;">
                    <i class="fas fa-plus mr-2 text-base" style="color: white;"></i>
                    <span style="color: white;">Create Habit</span>
                </button>
            </div>

            <!-- Time Picker Modal -->
            <div class="time-picker" id="timePicker">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Set Reminder Time</h3>
                        <button class="neumorphic-button w-8 h-8 flex items-center justify-center"
                            onclick="hideTimePicker()">
                            <i class="fas fa-times text-sm" style="color: var(--text-secondary);"></i>
                        </button>
                    </div>

                    <!-- Custom Time Input -->
                    <div class="mb-6">
                        <label class="text-sm mb-2 block" style="color: var(--text-secondary);">Choose any time:</label>
                        <input type="time" id="customTimeInput" class="neumorphic-inset w-full p-4 text-lg text-center"
                            style="color: var(--text-primary);" value="08:00" onchange="updateCustomTime()" />
                    </div>

                    <!-- Quick Presets -->
                    <div class="mb-4">
                        <label class="text-sm mb-3 block" style="color: var(--text-secondary);">Or choose a
                            preset:</label>
                        <div class="grid grid-cols-4 gap-2 mb-4">
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('06:00')">
                                <p class="text-sm" style="color: var(--text-primary);">6:00 AM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('07:00')">
                                <p class="text-sm" style="color: var(--text-primary);">7:00 AM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center selected" onclick="setPresetTime('08:00')">
                                <p class="text-sm" style="color: var(--text-primary);">8:00 AM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('09:00')">
                                <p class="text-sm" style="color: var(--text-primary);">9:00 AM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('12:00')">
                                <p class="text-sm" style="color: var(--text-primary);">12:00 PM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('18:00')">
                                <p class="text-sm" style="color: var(--text-primary);">6:00 PM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('20:00')">
                                <p class="text-sm" style="color: var(--text-primary);">8:00 PM</p>
                            </button>
                            <button class="neumorphic-button p-3 text-center" onclick="setPresetTime('22:00')">
                                <p class="text-sm" style="color: var(--text-primary);">10:00 PM</p>
                            </button>
                        </div>
                    </div>

                    <!-- Confirm Button -->
                    <button class="neumorphic-button w-full p-4 font-semibold" onclick="confirmTimeSelection()">
                        <span style="color: var(--text-primary);">Set Reminder Time</span>
                    </button>
                </div>
            </div>

            <!-- Custom Category Modal -->
            <div class="time-picker" id="customCategoryModal" style="display: none;">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Create Custom Category</h3>
                        <button class="neumorphic-button w-8 h-8 flex items-center justify-center"
                            onclick="hideCustomCategoryModal()">
                            <i class="fas fa-times text-sm" style="color: var(--text-secondary);"></i>
                        </button>
                    </div>

                    <!-- Category Name Input -->
                    <div class="mb-6">
                        <label class="text-sm mb-2 block" style="color: var(--text-secondary);">Category Name:</label>
                        <input type="text" id="customCategoryNameInput" class="neumorphic-inset w-full p-4 text-lg"
                            style="color: var(--text-primary);" placeholder="Enter category name" maxlength="20" />
                    </div>

                    <!-- Icon Selection -->
                    <div class="mb-6">
                        <label class="text-sm mb-3 block" style="color: var(--text-secondary);">Choose Icon:</label>
                        <div class="flex justify-between items-center mb-2">
                            <div class="text-xs" style="color: var(--text-secondary);">Showing <span id="iconCategoryName">Popular</span> icons</div>
                            <button class="neumorphic-button p-2 text-xs" onclick="toggleIconExpand()" id="expandIconsBtn">
                                <i class="fas fa-expand-alt"></i> <span>Expand</span>
                            </button>
                        </div>
                        <div class="mb-3 overflow-x-auto whitespace-nowrap" id="iconCategoriesScroll" style="scrollbar-width: thin;">
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn selected" onclick="filterIcons('popular')">Popular</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('activities')">Activities</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('health')">Health</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('food')">Food</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('tech')">Tech</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('travel')">Travel</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('nature')">Nature</button>
                            <button class="neumorphic-button p-2 text-xs mr-2 icon-category-btn" onclick="filterIcons('business')">Business</button>
                        </div>
                        <div class="icon-grid-container" style="max-height: 180px; overflow-y: auto; scrollbar-width: thin;">
                            <div class="grid grid-cols-6 gap-2" id="iconGrid">
                                <!-- Icons will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Color Selection -->
                    <div class="mb-6">
                        <label class="text-sm mb-3 block" style="color: var(--text-secondary);">Choose Color:</label>
                        <div class="flex justify-between items-center mb-2">
                            <div class="text-xs" style="color: var(--text-secondary);">30 colors available</div>
                            <button class="neumorphic-button p-2 text-xs" onclick="toggleColorExpand()" id="expandColorsBtn">
                                <i class="fas fa-expand-alt"></i> <span>Expand</span>
                            </button>
                        </div>
                        <div class="color-grid-container" style="max-height: 130px; overflow-y: auto; scrollbar-width: thin;">
                            <div class="grid grid-cols-6 gap-2" id="colorGrid">
                                <!-- Row 1 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ff383c" onclick="selectColor('#ff383c')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ff383c;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ff1744" onclick="selectColor('#ff1744')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ff1744;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#d50000" onclick="selectColor('#d50000')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #d50000;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ff8d28" onclick="selectColor('#ff8d28')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ff8d28;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ff5722" onclick="selectColor('#ff5722')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ff5722;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#e64a19" onclick="selectColor('#e64a19')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #e64a19;"></div>
                                </button>
                                
                                <!-- Row 2 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ffca28" onclick="selectColor('#ffca28')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ffca28;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ffd740" onclick="selectColor('#ffd740')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ffd740;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ffc107" onclick="selectColor('#ffc107')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ffc107;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ffa000" onclick="selectColor('#ffa000')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ffa000;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#ff8f00" onclick="selectColor('#ff8f00')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #ff8f00;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#f57f17" onclick="selectColor('#f57f17')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #f57f17;"></div>
                                </button>
                                
                                <!-- Row 3 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#34c759" onclick="selectColor('#34c759')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #34c759;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#4caf50" onclick="selectColor('#4caf50')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #4caf50;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#388e3c" onclick="selectColor('#388e3c')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #388e3c;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#8bc34a" onclick="selectColor('#8bc34a')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #8bc34a;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#7cb342" onclick="selectColor('#7cb342')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #7cb342;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#32d74b" onclick="selectColor('#32d74b')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #32d74b;"></div>
                                </button>
                                
                                <!-- Row 4 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#0088ff" onclick="selectColor('#0088ff')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #0088ff;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#007aff" onclick="selectColor('#007aff')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #007aff;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#2196f3" onclick="selectColor('#2196f3')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #2196f3;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#1976d2" onclick="selectColor('#1976d2')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #1976d2;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#0d47a1" onclick="selectColor('#0d47a1')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #0d47a1;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#00c8b3" onclick="selectColor('#00c8b3')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #00c8b3;"></div>
                                </button>
                                
                                <!-- Row 5 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#6155f5" onclick="selectColor('#6155f5')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #6155f5;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#af52de" onclick="selectColor('#af52de')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #af52de;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#cb30e0" onclick="selectColor('#cb30e0')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #cb30e0;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#673ab7" onclick="selectColor('#673ab7')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #673ab7;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#512da8" onclick="selectColor('#512da8')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #512da8;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#8e8e93" onclick="selectColor('#8e8e93')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #8e8e93;"></div>
                                </button>
                                
                                <!-- Row 6 -->
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#48484a" onclick="selectColor('#48484a')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #48484a;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#3a3a3c" onclick="selectColor('#3a3a3c')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #3a3a3c;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#e91e63" onclick="selectColor('#e91e63')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #e91e63;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#c2185b" onclick="selectColor('#c2185b')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #c2185b;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#009688" onclick="selectColor('#009688')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #009688;"></div>
                                </button>
                                <button class="neumorphic-button p-3 text-center color-option" data-color="#00bcd4" onclick="selectColor('#00bcd4')">
                                    <div class="w-6 h-6 rounded-full mx-auto" style="background-color: #00bcd4;"></div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Create Button -->
                    <button class="neumorphic-button w-full p-4 font-semibold" id="createCategoryButton" onclick="createCustomCategory()" disabled>
                        <span style="color: var(--text-primary);">Create Category</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Habit examples for each category
        const habitExamples = {
            health: [
                { text: "Take 1 vitamin", icon: "fas fa-pills" },
                { text: "Drive 3 water", icon: "fas fa-glass-water", timesPerDay: 3 },
                { text: "Take 3 deep breaths", icon: "fas fa-lungs" },
                { text: "Wash hands mindfully", icon: "fas fa-hands-wash" }
            ],
            fitness: [
                { text: "1 push-up", icon: "fas fa-dumbbell" },
                { text: "1 minute walk", icon: "fas fa-walking" },
                { text: "5 jumping jacks", icon: "fas fa-running" },
                { text: "10 seconds plank", icon: "fas fa-stopwatch" }
            ],
            mindfulness: [
                { text: "1 minute meditation", icon: "fas fa-lotus" },
                { text: "Write 1 gratitude", icon: "fas fa-heart" },
                { text: "5 mindful breaths", icon: "fas fa-leaf" },
                { text: "1 minute nature observation", icon: "fas fa-tree" }
            ],
            productivity: [
                { text: "Clear 1 email", icon: "fas fa-envelope" },
                { text: "Write 1 sentence", icon: "fas fa-pen" },
                { text: "Organize 1 item", icon: "fas fa-box" },
                { text: "Plan 1 task", icon: "fas fa-list-check" }
            ],
            wellness: [
                { text: "Apply moisturizer", icon: "fas fa-spa" },
                { text: "Stretch for 30 seconds", icon: "fas fa-child" },
                { text: "Smile in mirror", icon: "fas fa-smile" },
                { text: "Listen to 1 song", icon: "fas fa-music" }
            ],
            learning: [
                { text: "Read 1 page", icon: "fas fa-book-open" },
                { text: "Learn 1 new word", icon: "fas fa-language" },
                { text: "Watch 1 educational video", icon: "fas fa-play" },
                { text: "Practice 1 flashcard", icon: "fas fa-cards-blank" }
            ],
            nutrition: [
                { text: "Eat 1 piece of fruit", icon: "fas fa-apple-alt" },
                { text: "Have 1 healthy snack", icon: "fas fa-carrot" },
                { text: "Drink green tea", icon: "fas fa-mug-hot" },
                { text: "Eat 1 vegetable", icon: "fas fa-leaf" }
            ],
            sleep: [
                { text: "Set phone aside 5 min before bed", icon: "fas fa-mobile-alt" },
                { text: "Put on pajamas", icon: "fas fa-tshirt" },
                { text: "Turn off 1 light", icon: "fas fa-lightbulb" },
                { text: "Set tomorrow's alarm", icon: "fas fa-alarm-clock" }
            ]
        };

        // Global state
        let selectedCategory = null;
        let selectedFrequency = 'daily';
        let selectedTime = '8:00 AM';
        let customDays = [];
        let reminderEnabled = false;
        let timesPerDay = 1;

        // Custom category state
        let selectedIcon = 'fas fa-star';
        let selectedColor = '#ff383c';
        let customCategories = [];

        // Load custom categories from localStorage
        function loadCustomCategories() {
            try {
                const saved = localStorage.getItem('habitTracker_customCategories');
                customCategories = saved ? JSON.parse(saved) : [];
            } catch (error) {
                console.error('Error loading custom categories:', error);
                customCategories = [];
            }
        }

        // Save custom categories to localStorage
        function saveCustomCategories() {
            try {
                localStorage.setItem('habitTracker_customCategories', JSON.stringify(customCategories));
            } catch (error) {
                console.error('Error saving custom categories:', error);
            }
        }

        // Render category grid with default and custom categories
        function renderCategoryGrid() {
            const categoryGrid = document.getElementById('categoryGrid');
            
            // Default categories
            const defaultCategories = [
                { id: 'health', name: 'Health', icon: 'fas fa-heart', colorClass: 'health-color' },
                { id: 'fitness', name: 'Fitness', icon: 'fas fa-dumbbell', colorClass: 'fitness-color' },
                { id: 'mindfulness', name: 'Mindfulness', icon: 'fas fa-brain', colorClass: 'mindfulness-color' },
                { id: 'productivity', name: 'Productivity', icon: 'fas fa-tasks', colorClass: 'productivity-color' },
                { id: 'wellness', name: 'Wellness', icon: 'fas fa-spa', colorClass: 'wellness-color' },
                { id: 'learning', name: 'Learning', icon: 'fas fa-book', colorClass: 'learning-color' },
                { id: 'nutrition', name: 'Nutrition', icon: 'fas fa-apple-alt', colorClass: 'nutrition-color' },
                { id: 'sleep', name: 'Sleep', icon: 'fas fa-moon', colorClass: 'sleep-color' }
            ];

            let html = '';

            // Add default categories
            defaultCategories.forEach(cat => {
                html += `
                    <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="${cat.id}"
                        onclick="selectCategory('${cat.id}')">
                        <i class="${cat.icon} text-base ${cat.colorClass}"></i>
                        <span class="text-xs font-medium" style="color: var(--text-primary);">${cat.name}</span>
                    </button>
                `;
            });

            // Add custom categories
            customCategories.forEach(cat => {
                html += `
                    <button class="neumorphic-button p-2 flex items-center justify-start space-x-2" data-category="${cat.id}"
                        onclick="selectCategory('${cat.id}')">
                        <i class="${cat.icon} text-base" style="color: ${cat.color};"></i>
                        <span class="text-xs font-medium" style="color: var(--text-primary);">${cat.name}</span>
                    </button>
                `;
            });

            categoryGrid.innerHTML = html;
        }

        // Global state for icon and color expansion
        let isIconExpanded = false;
        let isColorExpanded = false;
        
        // Icon categories and their icons
        const iconCategories = {
            popular: [
                'fas fa-star', 'fas fa-heart', 'fas fa-home', 'fas fa-book', 
                'fas fa-dumbbell', 'fas fa-running', 'fas fa-apple-alt', 'fas fa-brain',
                'fas fa-tasks', 'fas fa-spa', 'fas fa-moon', 'fas fa-water'
            ],
            activities: [
                'fas fa-running', 'fas fa-walking', 'fas fa-hiking', 'fas fa-swimming-pool', 
                'fas fa-bicycle', 'fas fa-skiing', 'fas fa-skating', 'fas fa-dumbbell',
                'fas fa-basketball-ball', 'fas fa-football-ball', 'fas fa-volleyball-ball', 'fas fa-table-tennis',
                'fas fa-golf-ball', 'fas fa-biking', 'fas fa-snowboarding', 'fas fa-skating',
                'fas fa-gamepad', 'fas fa-chess', 'fas fa-dice', 'fas fa-paint-brush'
            ],
            health: [
                'fas fa-heartbeat', 'fas fa-medkit', 'fas fa-pills', 'fas fa-prescription-bottle', 
                'fas fa-stethoscope', 'fas fa-weight', 'fas fa-dumbbell', 'fas fa-running',
                'fas fa-bed', 'fas fa-moon', 'fas fa-spa', 'fas fa-smoking-ban',
                'fas fa-brain', 'fas fa-smile', 'fas fa-walking', 'fas fa-apple-alt'
            ],
            food: [
                'fas fa-apple-alt', 'fas fa-carrot', 'fas fa-bread-slice', 'fas fa-egg', 
                'fas fa-hamburger', 'fas fa-pizza-slice', 'fas fa-ice-cream', 'fas fa-cookie',
                'fas fa-candy-cane', 'fas fa-coffee', 'fas fa-mug-hot', 'fas fa-wine-glass',
                'fas fa-utensils', 'fas fa-glass-cheers', 'fas fa-glass-whiskey', 'fas fa-blender'
            ],
            tech: [
                'fas fa-laptop', 'fas fa-desktop', 'fas fa-mobile-alt', 'fas fa-tablet-alt', 
                'fas fa-headphones', 'fas fa-gamepad', 'fas fa-keyboard', 'fas fa-mouse',
                'fas fa-tv', 'fas fa-camera', 'fas fa-video', 'fas fa-microphone',
                'fas fa-server', 'fas fa-database', 'fas fa-wifi', 'fas fa-code'
            ],
            travel: [
                'fas fa-plane', 'fas fa-car', 'fas fa-bus', 'fas fa-train', 
                'fas fa-subway', 'fas fa-ship', 'fas fa-motorcycle', 'fas fa-bicycle',
                'fas fa-walking', 'fas fa-map-marker-alt', 'fas fa-compass', 'fas fa-map',
                'fas fa-globe-americas', 'fas fa-passport', 'fas fa-suitcase', 'fas fa-umbrella-beach'
            ],
            nature: [
                'fas fa-leaf', 'fas fa-tree', 'fas fa-seedling', 'fas fa-mountain', 
                'fas fa-cloud', 'fas fa-sun', 'fas fa-moon', 'fas fa-snowflake',
                'fas fa-water', 'fas fa-fire', 'fas fa-paw', 'fas fa-spider',
                'fas fa-fish', 'fas fa-frog', 'fas fa-feather', 'fas fa-wind'
            ],
            business: [
                'fas fa-briefcase', 'fas fa-chart-line', 'fas fa-chart-bar', 'fas fa-chart-pie', 
                'fas fa-calculator', 'fas fa-coins', 'fas fa-dollar-sign', 'fas fa-credit-card',
                'fas fa-file-invoice-dollar', 'fas fa-handshake', 'fas fa-user-tie', 'fas fa-building',
                'fas fa-calendar', 'fas fa-clock', 'fas fa-tasks', 'fas fa-clipboard-list'
            ]
        };
        
        // Function to filter icons by category
        function filterIcons(category) {
            // Check if the category exists in iconCategories
            if (!iconCategories[category]) {
                console.error(`Category '${category}' not found in iconCategories`);
                category = 'popular'; // Default to popular if category not found
            }
            
            // Update selected category button
            document.querySelectorAll('.icon-category-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // Find the button for this category and select it
            const categoryBtn = document.querySelector(`.icon-category-btn[onclick*="filterIcons('${category}')"]`);
            if (categoryBtn) {
                categoryBtn.classList.add('selected');
            }
            
            // Update category name display
            const iconCategoryNameElement = document.getElementById('iconCategoryName');
            if (iconCategoryNameElement) {
                iconCategoryNameElement.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            }
            
            // Populate icons
            populateIcons(category);
        }
        
        // Function to populate icons based on selected category
        function populateIcons(category) {
            const iconGrid = document.getElementById('iconGrid');
            let html = '';
            
            iconCategories[category].forEach(icon => {
                html += `
                    <button class="neumorphic-button p-3 text-center icon-option" data-icon="${icon}" onclick="selectIcon('${icon}')">
                        <i class="${icon} text-lg" style="color: var(--text-primary);"></i>
                    </button>
                `;
            });
            
            iconGrid.innerHTML = html;
            
            // If an icon is already selected, update the selection
            if (selectedIcon) {
                updateIconSelection();
            }
        }
        
        // Function to toggle icon grid expansion
        function toggleIconExpand() {
            const container = document.querySelector('.icon-grid-container');
            const btn = document.getElementById('expandIconsBtn');
            
            isIconExpanded = !isIconExpanded;
            
            if (isIconExpanded) {
                container.style.maxHeight = '250px'; // Reduced from 400px to fit iPhone frame
                btn.innerHTML = '<i class="fas fa-compress-alt"></i> <span>Collapse</span>';
            } else {
                container.style.maxHeight = '180px';
                btn.innerHTML = '<i class="fas fa-expand-alt"></i> <span>Expand</span>';
            }
        }
        
        // Function to toggle color grid expansion
        function toggleColorExpand() {
            const container = document.querySelector('.color-grid-container');
            const btn = document.getElementById('expandColorsBtn');
            
            isColorExpanded = !isColorExpanded;
            
            if (isColorExpanded) {
                container.style.maxHeight = '220px'; // Reduced from 300px to fit iPhone frame
                btn.innerHTML = '<i class="fas fa-compress-alt"></i> <span>Collapse</span>';
            } else {
                container.style.maxHeight = '130px';
                btn.innerHTML = '<i class="fas fa-expand-alt"></i> <span>Expand</span>';
            }
        }
        
        // Custom category modal functions
        function showCustomCategoryModal() {
            document.getElementById('customCategoryModal').style.display = 'block';
            // Reset form
            document.getElementById('customCategoryNameInput').value = '';
            selectedIcon = 'fas fa-star';
            selectedColor = '#ff383c';
            
            // Initialize icon grid with popular icons
            filterIcons('popular');
            
            updateColorSelection();
            validateCustomCategoryForm();
        }

        function hideCustomCategoryModal() {
            document.getElementById('customCategoryModal').style.display = 'none';
        }

        function selectIcon(icon) {
            selectedIcon = icon;
            updateIconSelection();
            validateCustomCategoryForm();
        }

        function selectColor(color) {
            selectedColor = color;
            updateColorSelection();
            validateCustomCategoryForm();
        }

        function updateIconSelection() {
            document.querySelectorAll('.icon-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // Find the button with the selected icon
            const selectedButton = document.querySelector(`[data-icon="${selectedIcon}"]`);
            
            // If the button exists in the current view, select it
            if (selectedButton) {
                selectedButton.classList.add('selected');
            } else {
                // If the selected icon is not in the current category view,
                // find which category it belongs to and switch to that category
                for (const [category, icons] of Object.entries(iconCategories)) {
                    if (icons.includes(selectedIcon)) {
                        filterIcons(category);
                        // Now the button should be in the DOM, so select it
                        document.querySelector(`[data-icon="${selectedIcon}"]`).classList.add('selected');
                        break;
                    }
                }
            }
        }

        function updateColorSelection() {
            document.querySelectorAll('.color-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-color="${selectedColor}"]`).classList.add('selected');
        }

        function validateCustomCategoryForm() {
            const nameInput = document.getElementById('customCategoryNameInput');
            const createButton = document.getElementById('createCategoryButton');
            
            const name = nameInput.value.trim();
            const isValid = name.length > 0 && selectedIcon && selectedColor;
            
            createButton.disabled = !isValid;
            createButton.style.opacity = isValid ? '1' : '0.5';
        }

        function createCustomCategory() {
            const nameInput = document.getElementById('customCategoryNameInput');
            const name = nameInput.value.trim();
            
            if (!name || !selectedIcon || !selectedColor) {
                alert('Please fill in all fields');
                return;
            }

            // Check if category name already exists
            const existingCategory = customCategories.find(cat => cat.name.toLowerCase() === name.toLowerCase());
            if (existingCategory) {
                alert('A category with this name already exists');
                return;
            }

            // Create new category
            const newCategory = {
                id: 'custom_' + Date.now(),
                name: name,
                icon: selectedIcon,
                color: selectedColor
            };

            customCategories.push(newCategory);
            saveCustomCategories();
            renderCategoryGrid();
            hideCustomCategoryModal();

            // Auto-select the new category
            selectCategory(newCategory.id);
        }

        // Add event listeners for custom category modal
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('customCategoryNameInput');
            if (nameInput) {
                nameInput.addEventListener('input', validateCustomCategoryForm);
            }
            
            // Initialize the icon grid with popular icons
            if (document.getElementById('iconGrid')) {
                filterIcons('popular');
            }
        });

        function changeTimesPerDay(delta) {
            timesPerDay = Math.max(1, timesPerDay + delta);
            document.getElementById('timesPerDayInput').value = timesPerDay;
        }

        function editTimesPerDay() {
            const newValue = prompt('Enter times per day (minimum 1):', timesPerDay);
            const intValue = parseInt(newValue, 10);
            if (!isNaN(intValue) && intValue >= 1) {
                timesPerDay = intValue;
                document.getElementById('timesPerDayInput').value = timesPerDay;
            }
        }

        // Category selection
        function selectCategory(category) {
            // If already selected, unselect
            if (selectedCategory === category) {
                selectedCategory = null;
                // Unselect all buttons
                document.querySelectorAll('[data-category]').forEach(btn => {
                    btn.classList.remove('selected');
                });
                // Hide examples
                document.getElementById('examplesSection').style.display = 'none';
                // Clear input and hint
                document.getElementById('habitInput').value = '';
                document.getElementById('inputHint').textContent = 'Select a category to see suggestions';
                // Disable create button
                validateForm();
                return;
            }
            selectedCategory = category;

            // Update UI
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('selected');

            // Show examples
            showExamples(category);

            // Update input hint
            if (habitExamples[category]) {
                document.getElementById('inputHint').textContent = `Try: ${habitExamples[category][0].text}`;
            } else {
                // Custom category
                const customCategory = customCategories.find(cat => cat.id === category);
                if (customCategory) {
                    document.getElementById('inputHint').textContent = `Create a habit for ${customCategory.name}`;
                }
            }

            // Validate form
            validateForm();
        }

        // Show category examples and user-created habits
        function showExamples(category) {
            const examplesSection = document.getElementById('examplesSection');
            const examplesTitle = document.getElementById('examplesTitle');
            const examplesList = document.getElementById('examplesList');

            // Add edit/save buttons to the top right
            // Remove any previous edit panel not in examplesSection
            let oldPanel = document.getElementById('examplesEditPanel');
            if (oldPanel && oldPanel.parentElement !== examplesSection) {
                oldPanel.parentElement.removeChild(oldPanel);
                oldPanel = null;
            }
            let editPanel = document.getElementById('examplesEditPanel');
            if (!editPanel) {
                editPanel = document.createElement('div');
                editPanel.id = 'examplesEditPanel';
                editPanel.style.position = 'absolute';
                editPanel.style.top = '18px';
                editPanel.style.right = '24px';
                editPanel.style.zIndex = '10';
                examplesSection.appendChild(editPanel);
            }
            let isEditing = window.examplesEditing || false;
            editPanel.innerHTML = isEditing
                ? `<button class="neumorphic-button p-2 text-xs font-semibold" id="saveExamplesBtn" style="min-width:44px;" onclick="saveUserHabitsEdit()"><i class='fas fa-save'></i></button>`
                : `<button class="neumorphic-button p-2 text-xs font-semibold" id="editExamplesBtn" style="min-width:44px;" onclick="toggleExamplesEdit()"><i class='fas fa-pen'></i></button>`;

            examplesSection.style.position = 'relative';
            
            // Check if it's a custom category and set the title accordingly
            if (category.startsWith('custom_')) {
                const customCategory = customCategories.find(cat => cat.id === category);
                if (customCategory) {
                    examplesTitle.textContent = `${customCategory.name} Examples`;
                } else {
                    examplesTitle.textContent = 'Custom Examples';
                }
            } else {
                examplesTitle.textContent = `${category.charAt(0).toUpperCase() + category.slice(1)} Examples`;
            }

            // Fetch user-created habits for this category
            let userHabits = [];
            try {
                const allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                userHabits = allHabits.filter(h => h.category === category);
            } catch (e) {
                userHabits = [];
            }

            let userHabitsHtml = '';
            if (userHabits.length > 0) {
                userHabitsHtml = `
                    <div class="mb-2">
                        <div class="text-xs font-semibold mb-1 flex justify-between items-center" style="color: var(--text-secondary);">Your Habits</div>
                        ${userHabits.map((habit, idx) => {
                            const safeName = habit.name.replace(/'/g, "&#39;").replace(/\"/g, '&quot;');
                            if (window.examplesEditing) {
                                return `<div class=\"flex items-center space-x-2 mb-2\">\n\
                                    <input type=\"text\" class=\"neumorphic-inset flex-1 p-3 text-sm\" style=\"color: var(--text-primary); min-width:0;\" value=\"${habit.name.replace(/"/g, '&quot;')}\" data-edit-idx=\"${idx}\" />\n\
                                    <button class=\"neumorphic-button p-2\" style=\"color:var(--error);\" onclick=\"deleteUserHabit(${idx})\"><i class='fas fa-trash'></i></button>\n\
                                </div>`;
                            } else {
                                return `<button class=\"neumorphic-button p-3 flex items-center justify-between w-full\" onclick=\"selectExample(\'${safeName}\')\">\n\
                                    <div class=\"flex items-center space-x-3\">\n\
                                        <i class=\"fas fa-user text-sm\" style=\"color: var(--${category});\"></i>\n\
                                        <span class=\"text-sm\" style=\"color: var(--text-primary);\">${habit.name}</span>\n\
                                    </div>\n\
                                    <i class=\"fas fa-arrow-right text-xs\" style=\"color: var(--text-secondary);\"></i>\n\
                                </button>`;
                            }
                        }).join('')}
                    </div>
                `;
            }

            let exampleHabitsHtml = '';
            
            // Check if it's a custom category or default category
            if (habitExamples[category]) {
                // Default category with examples
                exampleHabitsHtml = habitExamples[category].map(example => {
                    const safeText = example.text.replace(/'/g, "&#39;").replace(/\"/g, '&quot;');
                    return `<button class=\"neumorphic-button p-3 flex items-center justify-between w-full\" onclick=\"selectExample(\'${safeText}\')\">\n\
                        <div class=\"flex items-center space-x-3\">\n\
                            <i class=\"${example.icon} text-sm\" style=\"color: var(--${category});\"></i>\n\
                            <span class=\"text-sm\" style=\"color: var(--text-primary);\">${example.text}</span>\n\
                        </div>\n\
                        <i class=\"fas fa-arrow-right text-xs\" style=\"color: var(--text-secondary);\"></i>\n\
                    </button>`;
                }).join('');
            } else {
                // Custom category - find the category details
                const customCategory = customCategories.find(cat => cat.id === category);
                if (customCategory && userHabits.length === 0) {
                    exampleHabitsHtml = `<div class="text-center py-4">
                        <p class="text-sm" style="color: var(--text-secondary);">No examples yet for this custom category.</p>
                        <p class="text-xs mt-1" style="color: var(--text-muted);">Create your first habit above!</p>
                    </div>`;
                }
            }

            examplesList.innerHTML = userHabitsHtml + exampleHabitsHtml;

            examplesSection.style.display = 'block';
            examplesSection.classList.add('fade-in');
        }

        // Toggle edit mode for user habits
        function toggleExamplesEdit() {
            window.examplesEditing = true;
            showExamples(selectedCategory);
        }

        // Save edits to user habits
        function saveUserHabitsEdit() {
            const inputs = document.querySelectorAll('[data-edit-idx]');
            let allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
            let userHabits = allHabits.filter(h => h.category === selectedCategory);
            let otherHabits = allHabits.filter(h => h.category !== selectedCategory);
            // Update names from inputs
            userHabits.forEach((habit, idx) => {
                const input = Array.from(inputs).find(inp => parseInt(inp.getAttribute('data-edit-idx')) === idx);
                if (input) {
                    habit.name = input.value.trim();
                }
            });
            // Remove empty names
            userHabits = userHabits.filter(h => h.name !== '');
            // Save back
            localStorage.setItem('habitTracker_habits', JSON.stringify([...otherHabits, ...userHabits]));
            window.examplesEditing = false;
            showExamples(selectedCategory);
        }

        // Delete a user habit by index
        function deleteUserHabit(idx) {
            let allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
            let userHabits = allHabits.filter(h => h.category === selectedCategory);
            let otherHabits = allHabits.filter(h => h.category !== selectedCategory);
            userHabits.splice(idx, 1);
            localStorage.setItem('habitTracker_habits', JSON.stringify([...otherHabits, ...userHabits]));
            showExamples(selectedCategory);
        }

        // Select example or user habit and apply to input
        function selectExample(text) {
            // Remove HTML entities if present
            const cleanText = text.replace(/&#39;/g, "'").replace(/&quot;/g, '"');
            document.getElementById('habitInput').value = cleanText;

            // If the example is 'Drive 3 water', set timesPerDay to 3
            if (cleanText === 'Drive 3 water') {
                timesPerDay = 3;
                document.getElementById('timesPerDayInput').value = timesPerDay;
            }
            validateForm();
        }

        // Frequency selection
        function selectFrequency(frequency) {
            selectedFrequency = frequency;

            document.querySelectorAll('[data-frequency]').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-frequency="${frequency}"]`).classList.add('selected');

            // Show/hide custom days section
            const customDaysSection = document.getElementById('customDaysSection');
            if (frequency === 'custom') {
                customDaysSection.style.display = 'block';
                // Pre-select weekdays as default for custom
                if (customDays.length === 0) {
                    ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'].forEach(day => {
                        toggleDay(day);
                    });
                }
            } else {
                customDaysSection.style.display = 'none';
                // Clear custom days when switching away from custom
                customDays = [];
                document.querySelectorAll('[data-day]').forEach(btn => {
                    btn.classList.remove('selected');
                });
                updateSelectedDaysText();
            }
        }

        // Toggle day selection for custom frequency
        function toggleDay(day) {
            const dayButton = document.querySelector(`[data-day="${day}"]`);

            if (customDays.includes(day)) {
                // Remove day
                customDays = customDays.filter(d => d !== day);
                dayButton.classList.remove('selected');
            } else {
                // Add day
                customDays.push(day);
                dayButton.classList.add('selected');
            }

            updateSelectedDaysText();
            validateForm();
        }

        // Update selected days text
        function updateSelectedDaysText() {
            const selectedDaysText = document.getElementById('selectedDaysText');
            const dayNames = {
                monday: 'Mon',
                tuesday: 'Tue',
                wednesday: 'Wed',
                thursday: 'Thu',
                friday: 'Fri',
                saturday: 'Sat',
                sunday: 'Sun'
            };

            if (customDays.length === 0) {
                selectedDaysText.textContent = 'No days selected';
                selectedDaysText.style.color = 'var(--error)';
            } else if (customDays.length === 7) {
                selectedDaysText.textContent = 'Every day';
                selectedDaysText.style.color = 'var(--text-muted)';
            } else {
                const daysList = customDays.map(day => dayNames[day]).join(', ');
                selectedDaysText.textContent = `${customDays.length} days: ${daysList}`;
                selectedDaysText.style.color = 'var(--text-muted)';
            }
        }

        // Reminder toggle function
        function toggleReminder() {
            reminderEnabled = !reminderEnabled;
            const toggle = document.getElementById('reminderToggle');
            const timeSection = document.getElementById('reminderTimeSection');
            const disabledSection = document.getElementById('reminderDisabledSection');

            if (reminderEnabled) {
                toggle.classList.add('active');
                timeSection.style.display = 'block';
                disabledSection.style.display = 'none';
            } else {
                toggle.classList.remove('active');
                timeSection.style.display = 'none';
                disabledSection.style.display = 'block';
            }
        }

        // Time picker functions
        function showTimePicker() {
            if (!reminderEnabled) return;

            // Update time input to current selected time
            const timeInput = document.getElementById('customTimeInput');
            const currentTime = convertDisplayTimeToInput(selectedTime);
            timeInput.value = currentTime;

            document.getElementById('timePicker').style.display = 'block';
        }

        function hideTimePicker() {
            document.getElementById('timePicker').style.display = 'none';
        }

        function setPresetTime(time24) {
            const timeInput = document.getElementById('customTimeInput');
            timeInput.value = time24;
            updateCustomTime();

            // Update preset button selection
            document.querySelectorAll('#timePicker button[onclick*="setPresetTime"]').forEach(btn => {
                btn.classList.remove('selected');
            });
            event.target.classList.add('selected');
        }

        function updateCustomTime() {
            const timeInput = document.getElementById('customTimeInput');
            const time24 = timeInput.value;

            if (time24) {
                selectedTime = convertInputTimeToDisplay(time24);
                document.getElementById('selectedTime').textContent = selectedTime;

                // Clear preset button selections since user typed custom time
                document.querySelectorAll('#timePicker button[onclick*="setPresetTime"]').forEach(btn => {
                    btn.classList.remove('selected');
                });
            }
        }

        function confirmTimeSelection() {
            updateCustomTime();
            hideTimePicker();
        }

        // Time format conversion helpers
        function convertInputTimeToDisplay(time24) {
            const [hours, minutes] = time24.split(':');
            const hour12 = parseInt(hours);
            const period = hour12 >= 12 ? 'PM' : 'AM';
            const displayHour = hour12 === 0 ? 12 : hour12 > 12 ? hour12 - 12 : hour12;
            return `${displayHour}:${minutes} ${period}`;
        }

        function convertDisplayTimeToInput(timeDisplay) {
            const [time, period] = timeDisplay.split(' ');
            const [hours, minutes] = time.split(':');
            let hour24 = parseInt(hours);

            if (period === 'PM' && hour24 !== 12) {
                hour24 += 12;
            } else if (period === 'AM' && hour24 === 12) {
                hour24 = 0;
            }

            return `${hour24.toString().padStart(2, '0')}:${minutes}`;
        }

        // Input validation
        function validateInput() {
            const input = document.getElementById('habitInput');
            if (input.value.trim() === '') {
                input.classList.add('error-state');
            } else {
                input.classList.remove('error-state');
            }
            validateForm();
        }

        // Form validation
        function validateForm() {
            const habitInput = document.getElementById('habitInput');
            const createButton = document.getElementById('createButton');

            const hasCategory = selectedCategory !== null;
            const hasHabitName = habitInput.value.trim() !== '';
            const hasValidFrequency = selectedFrequency !== 'custom' || customDays.length > 0;

            const isValid = hasCategory && hasHabitName && hasValidFrequency;

            createButton.disabled = !isValid;
            createButton.style.opacity = isValid ? '1' : '0.5';
        }

        // Times per day change handler
        function changeTimesPerDay(delta) {
            const input = document.getElementById('timesPerDayInput');
            let currentValue = parseInt(input.value);

            // Update value with bounds checking
            currentValue = Math.max(1, Math.min(10, currentValue + delta));
            input.value = currentValue;

            // Update global state
            timesPerDay = currentValue;
        }

        // Edit times per day field
        function editTimesPerDay() {
            const input = document.getElementById('timesPerDayInput');
            input.removeAttribute('readonly');
            input.focus();

            // Select all text for easy editing
            input.select();

            // Handle blur event to save changes
            input.onblur = function () {
                input.setAttribute('readonly', 'readonly');
                const newValue = parseInt(input.value);
                if (!isNaN(newValue)) {
                    // Update global state
                    timesPerDay = Math.max(1, Math.min(10, newValue));
                } else {
                    // Revert to previous value if invalid
                    input.value = timesPerDay;
                }
            };
        }

        // Create or update habit
        function createHabit() {
            if (!selectedCategory || !document.getElementById('habitInput').value.trim()) {
                alert('Please select a category and enter a habit name');
                return;
            }

            if (selectedFrequency === 'custom' && customDays.length === 0) {
                alert('Please select at least one day for custom frequency');
                return;
            }

            const habitName = document.getElementById('habitInput').value.trim();
            const createButton = document.getElementById('createButton');
            const editHabitId = createButton.getAttribute('data-edit-id');
            
            // Check if we're in edit mode
            if (editHabitId) {
                // Update existing habit
                updateExistingHabit(parseInt(editHabitId), habitName);
            } else {
                // Create new habit
                createNewHabit(habitName);
            }

            // Navigate to dashboard
            window.location.href = 'dashboard.html';
        }

        // Create new habit function
        function createNewHabit(habitName) {
            // Check if the habit name is in the default examples for the selected category
            // Only check for default categories, not custom ones
            const isExample = habitExamples[selectedCategory] ? 
                habitExamples[selectedCategory].some(e => e.text === habitName) : 
                false;

            // Only save if not an example habit (i.e., user created or modified)
            if (!isExample) {
                const habitData = {
                    id: Date.now(), // Simple ID generation
                    category: selectedCategory,
                    name: habitName,
                    subtitle: `${timesPerDay > 1 ? `${timesPerDay} times daily` : 'Daily goal'}`,
                    frequency: selectedFrequency,
                    customDays: selectedFrequency === 'custom' ? customDays : null,
                    reminderEnabled: reminderEnabled,
                    reminderTime: reminderEnabled ? selectedTime : null,
                    timesPerDay: timesPerDay,
                    todayCount: 0,
                    completed: false,
                    streak: 0,
                    startDaysAgo: 0,
                    history: [],
                    createdAt: new Date().toISOString()
                };

                // Save to localStorage
                let existingHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                existingHabits.push(habitData);
                localStorage.setItem('habitTracker_habits', JSON.stringify(existingHabits));

                // Show success message
                alert(`Habit "${habitData.name}" created successfully!`);
            } else {
                // If it's an example, just show a message
                alert('This is a default example habit. Please modify the name to create your own habit.');
            }
        }

        // Update existing habit function
        function updateExistingHabit(habitId, habitName) {
            try {
                let existingHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                const habitIndex = existingHabits.findIndex(h => h.id === habitId);
                
                if (habitIndex !== -1) {
                    // Update the habit while preserving important data like history and streak
                    const existingHabit = existingHabits[habitIndex];
                    existingHabits[habitIndex] = {
                        ...existingHabit,
                        category: selectedCategory,
                        name: habitName,
                        subtitle: `${timesPerDay > 1 ? `${timesPerDay} times daily` : 'Daily goal'}`,
                        frequency: selectedFrequency,
                        customDays: selectedFrequency === 'custom' ? customDays : null,
                        reminderEnabled: reminderEnabled,
                        reminderTime: reminderEnabled ? selectedTime : null,
                        timesPerDay: timesPerDay,
                        updatedAt: new Date().toISOString()
                    };

                    // Save to localStorage
                    localStorage.setItem('habitTracker_habits', JSON.stringify(existingHabits));

                    // Show success message
                    alert(`Habit "${habitName}" updated successfully!`);
                } else {
                    alert('Habit not found. Creating as new habit instead.');
                    createNewHabit(habitName);
                }
            } catch (error) {
                console.error('Error updating habit:', error);
                alert('Error updating habit. Please try again.');
            }
        }

        // Navigation functions
        function goBack() {
            window.location.href = 'dashboard.html';
        }

        function showHelp() {
            alert('Mini Habits Tips:\n\n• Start incredibly small\n• Be consistent over perfect\n• Celebrate small wins\n• Link to existing habits\n• Focus on the process, not results');
        }

        // Initialize form validation and handle edit mode
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOMContentLoaded fired');
            
            // Load custom categories and render grid
            loadCustomCategories();
            renderCategoryGrid();
            
            // Check if we're in edit mode
            const urlParams = new URLSearchParams(window.location.search);
            const editHabitId = urlParams.get('edit');
            console.log('Edit habit ID from URL:', editHabitId);
            
            if (editHabitId && editHabitId !== '1') {
                console.log('Loading habit for edit with ID:', editHabitId);
                // Load specific habit for editing
                loadHabitForEdit(parseInt(editHabitId));
            } else {
                console.log('Not in edit mode or invalid edit ID');
            }
            
            validateForm();
        });

        // Load habit data for editing
        function loadHabitForEdit(habitId) {
            console.log('Loading habit for edit, ID:', habitId);
            try {
                const allHabits = JSON.parse(localStorage.getItem('habitTracker_habits') || '[]');
                console.log('All habits:', allHabits);
                const habitToEdit = allHabits.find(h => h.id === habitId);
                console.log('Habit to edit:', habitToEdit);
                
                if (habitToEdit) {
                    // Update page title and header
                    document.title = 'HabitTest - Edit Habit';
                    const titleElement = document.querySelector('h1');
                    if (titleElement) {
                        titleElement.textContent = 'Edit Habit';
                        const subtitleElement = titleElement.nextElementSibling;
                        if (subtitleElement) {
                            subtitleElement.textContent = 'Update your habit';
                        }
                    }
                    
                    // Update button
                    const createButton = document.getElementById('createButton');
                    if (createButton) {
                        createButton.innerHTML = '<i class="fas fa-save mr-2 text-base" style="color: white;"></i><span style="color: white;">Update Habit</span>';
                        createButton.setAttribute('data-edit-id', habitId);
                    }
                    
                    // Pre-populate form fields
                    selectedCategory = habitToEdit.category;
                    selectedFrequency = habitToEdit.frequency || 'daily';
                    selectedTime = habitToEdit.reminderTime || '8:00 AM';
                    customDays = habitToEdit.customDays || [];
                    reminderEnabled = habitToEdit.reminderEnabled || false;
                    timesPerDay = habitToEdit.timesPerDay || 1;
                    
                    // Set category with a delay to ensure DOM is ready
                    setTimeout(() => {
                        if (selectedCategory) {
                            selectCategory(selectedCategory);
                        }
                        
                        // Set habit name
                        const habitInput = document.getElementById('habitInput');
                        if (habitInput) {
                            habitInput.value = habitToEdit.name;
                        }
                        
                        // Set times per day
                        const timesInput = document.getElementById('timesPerDayInput');
                        if (timesInput) {
                            timesInput.value = timesPerDay;
                        }
                        
                        // Set frequency
                        selectFrequency(selectedFrequency);
                        
                        // Set reminder
                        if (reminderEnabled) {
                            toggleReminder();
                            const timeElement = document.getElementById('selectedTime');
                            if (timeElement) {
                                timeElement.textContent = selectedTime;
                            }
                        }
                        
                        // Validate form to enable update button
                        validateForm();
                    }, 100);
                } else {
                    console.error('Habit not found with ID:', habitId);
                    alert('Habit not found. Redirecting to dashboard.');
                    window.location.href = 'dashboard.html';
                }
            } catch (error) {
                console.error('Error loading habit for edit:', error);
                alert('Error loading habit data. Redirecting to dashboard.');
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>

</html>