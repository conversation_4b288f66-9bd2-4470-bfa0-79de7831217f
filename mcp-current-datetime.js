// Minimal MCP (Model Context Protocol) for current date and time
// Returns date/time in 'YYYY-MM-DD HH-mm-ss' format

function getCurrentDateTime() {
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const year = now.getFullYear();
    const month = pad(now.getMonth() + 1);
    const day = pad(now.getDate());
    const hour = pad(now.getHours());
    const min = pad(now.getMinutes());
    const sec = pad(now.getSeconds());
    return `${year}-${month}-${day} ${hour}-${min}-${sec}`;
}

// Example usage:
console.log(getCurrentDateTime());

// Export for MCP usage (if needed)
if (typeof module !== 'undefined') {
    module.exports = { getCurrentDateTime };
}
