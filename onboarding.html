<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Onboarding</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button:active {
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button:active {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button:hover {
            transform: translateY(-2px);
            box-shadow: 12px 12px 20px rgba(163, 177, 198, 0.7), -12px -12px 20px rgba(255, 255, 255, 0.6);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button:hover {
                transform: translateY(-3px) scale(1.02);
                box-shadow: 12px 12px 20px rgba(0, 0, 0, 0.9), -12px -12px 20px rgba(51, 51, 51, 0.4);
            }
        }
        
        .grid-cell {
            width: 12px;
            height: 12px;
            border-radius: 3px;
            margin: 1px;
        }
        
        .grid-cell-0 { 
            background: var(--bg-primary);
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.4), inset -2px -2px 4px rgba(255, 255, 255, 0.3);
        }
        
        @media (prefers-color-scheme: dark) {
            .grid-cell-0 {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.6), inset -2px -2px 4px rgba(51, 51, 51, 0.2);
            }
        }
        
        .grid-cell-1 { background: #c6e48b; }
        .grid-cell-2 { background: #7bc96f; }
        .grid-cell-3 { background: #4c9f50; }
        .grid-cell-4 { background: #2d5a37; }
        
        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }
        
        @keyframes pulse-soft {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        @keyframes glow-pulse {
            0%, 100% { 
                box-shadow: 0 0 5px var(--mindfulness), 0 0 10px var(--mindfulness), 0 0 15px var(--mindfulness);
            }
            50% { 
                box-shadow: 0 0 10px var(--mindfulness), 0 0 20px var(--mindfulness), 0 0 30px var(--mindfulness);
            }
        }
        
        .pulse-animation {
            animation: pulse-soft 3s ease-in-out infinite;
        }
        
        @media (prefers-color-scheme: dark) {
            .pulse-animation {
                animation: glow-pulse 3s ease-in-out infinite;
            }
        }
    </style>
</head>
<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">
            
            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 py-8 min-h-screen">
                
                <!-- Logo Section -->
                <div class="text-center mb-12">
                    <div class="neumorphic-card w-20 h-20 mx-auto flex items-center justify-center mb-6 pulse-animation">
                        <i class="fas fa-seedling text-3xl" style="color: var(--success);"></i>
                    </div>
                    <h1 class="text-4xl font-bold mb-2" style="color: var(--text-primary);">HabitTest</h1>
                    <p class="text-lg font-medium" style="color: var(--text-secondary);">Too Small to Fail</p>
                </div>

                <!-- Demo Grid -->
                <div class="neumorphic-card p-6 mb-8">
                    <h3 class="text-lg font-semibold mb-4 text-center" style="color: var(--text-primary);">Your Journey Visualized</h3>
                    <div class="flex justify-center">
                        <div class="grid grid-cols-7 gap-1">
                            <!-- Generate demo habit grid -->
                            <div class="grid-cell grid-cell-3"></div>
                            <div class="grid-cell grid-cell-2"></div>
                            <div class="grid-cell grid-cell-4"></div>
                            <div class="grid-cell grid-cell-1"></div>
                            <div class="grid-cell grid-cell-3"></div>
                            <div class="grid-cell grid-cell-0"></div>
                            <div class="grid-cell grid-cell-2"></div>
                            
                            <div class="grid-cell grid-cell-1"></div>
                            <div class="grid-cell grid-cell-4"></div>
                            <div class="grid-cell grid-cell-2"></div>
                            <div class="grid-cell grid-cell-3"></div>
                            <div class="grid-cell grid-cell-1"></div>
                            <div class="grid-cell grid-cell-2"></div>
                            <div class="grid-cell grid-cell-0"></div>
                            
                            <div class="grid-cell grid-cell-2"></div>
                            <div class="grid-cell grid-cell-3"></div>
                            <div class="grid-cell grid-cell-1"></div>
                            <div class="grid-cell grid-cell-4"></div>
                            <div class="grid-cell grid-cell-2"></div>
                            <div class="grid-cell grid-cell-3"></div>
                            <div class="grid-cell grid-cell-1"></div>
                        </div>
                    </div>
                    <p class="text-center text-sm mt-4" style="color: var(--text-secondary);">GitHub-style progress tracking</p>
                </div>

                <!-- Key Benefits -->
                <div class="space-y-4 mb-12">
                    <div class="neumorphic-card p-4 flex items-center">
                        <div class="neumorphic-button w-12 h-12 flex items-center justify-center mr-4">
                            <i class="fas fa-feather text-lg" style="color: var(--wellness);"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" style="color: var(--text-primary);">Too Small to Fail</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Micro-habits that stick</p>
                        </div>
                    </div>

                    <div class="neumorphic-card p-4 flex items-center">
                        <div class="neumorphic-button w-12 h-12 flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-lg" style="color: var(--mindfulness);"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" style="color: var(--text-primary);">Privacy First</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">No accounts, just progress</p>
                        </div>
                    </div>

                    <div class="neumorphic-card p-4 flex items-center">
                        <div class="neumorphic-button w-12 h-12 flex items-center justify-center mr-4">
                            <i class="fas fa-heart text-lg" style="color: var(--success);"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" style="color: var(--text-primary);">Built for Real Life</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Interruptions won't break you</p>
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <div class="fixed bottom-8 left-4 right-4">
                    <button class="neumorphic-button w-full py-4 font-semibold text-lg" style="color: var(--text-primary);">
                        Start Your Journey
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 