<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - 4-Week Plan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-inset {
            box-shadow: inset 6px 6px 10px rgba(163, 177, 198, 0.6), inset -6px -6px 10px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-inset {
                box-shadow: inset 6px 6px 10px rgba(0, 0, 0, 0.8), inset -6px -6px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .week-completed {
            background: var(--success);
            border-radius: 50%;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .week-completed {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .week-current {
            background: var(--warning);
            border-radius: 50%;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .week-current {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .week-pending {
            background: var(--bg-primary);
            border: 2px solid var(--text-muted);
            border-radius: 50%;
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.4), inset -2px -2px 4px rgba(255, 255, 255, 0.3);
        }
        
        @media (prefers-color-scheme: dark) {
            .week-pending {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.6), inset -2px -2px 4px rgba(51, 51, 51, 0.2);
            }
        }
        
        .progress-bar {
            height: 6px;
            background: var(--bg-primary);
            border-radius: 3px;
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
            overflow: hidden;
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-bar {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success), var(--wellness));
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
            .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }
        
        .day-completed {
            background: var(--success);
            border-radius: 50%;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .day-completed {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }
        
        .day-pending {
            background: var(--bg-primary);
            border: 1px solid var(--text-muted);
            border-radius: 50%;
            box-shadow: inset 1px 1px 2px rgba(163, 177, 198, 0.4), inset -1px -1px 2px rgba(255, 255, 255, 0.3);
        }
        
        @media (prefers-color-scheme: dark) {
            .day-pending {
                box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.6), inset -1px -1px 2px rgba(51, 51, 51, 0.2);
            }
        }
    </style>
</head>
<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">
            
            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">
                
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">4-Week Plan</h1>
                        <p class="text-sm" style="color: var(--text-secondary);">Build Your Foundation</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-info-circle text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>

                <!-- Current Plan Overview -->
                <div class="neumorphic-card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Fitness Foundation</h3>
                            <p class="text-sm" style="color: var(--text-secondary);">Building daily movement habits</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold" style="color: var(--warning);">70%</div>
                            <div class="text-xs" style="color: var(--text-secondary);">Complete</div>
                        </div>
                    </div>
                    
                    <div class="progress-bar mb-4">
                        <div class="progress-fill" style="width: 70%;"></div>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm">
                        <span style="color: var(--text-secondary);">Week 3 of 4</span>
                        <span style="color: var(--success);">19 days completed</span>
                    </div>
                </div>

                <!-- Week Timeline -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Weekly Progress</h3>
                    
                    <div class="space-y-4">
                        <!-- Week 1 -->
                        <div class="flex items-center space-x-4">
                            <div class="week-completed w-10 h-10 flex items-center justify-center">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Week 1: Foundation</h4>
                                    <span class="text-xs" style="color: var(--success);">✓ Completed</span>
                                </div>
                                <p class="text-sm" style="color: var(--text-secondary);">1 push-up daily • 7/7 days</p>
                                <div class="flex space-x-1 mt-2">
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Week 2 -->
                        <div class="flex items-center space-x-4">
                            <div class="week-completed w-10 h-10 flex items-center justify-center">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Week 2: Consistency</h4>
                                    <span class="text-xs" style="color: var(--success);">✓ Completed</span>
                                </div>
                                <p class="text-sm" style="color: var(--text-secondary);">2 push-ups daily • 7/7 days</p>
                                <div class="flex space-x-1 mt-2">
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Week 3 -->
                        <div class="flex items-center space-x-4">
                            <div class="week-current w-10 h-10 flex items-center justify-center">
                                <span class="text-white text-sm font-bold">3</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Week 3: Building Momentum</h4>
                                    <span class="text-xs" style="color: var(--warning);">● In Progress</span>
                                </div>
                                <p class="text-sm" style="color: var(--text-secondary);">3 push-ups daily • 5/7 days</p>
                                <div class="flex space-x-1 mt-2">
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-completed w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Week 4 -->
                        <div class="flex items-center space-x-4">
                            <div class="week-pending w-10 h-10 flex items-center justify-center">
                                <span class="text-xs font-bold" style="color: var(--text-muted);">4</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-medium" style="color: var(--text-primary);">Week 4: Mastery</h4>
                                    <span class="text-xs" style="color: var(--text-muted);">○ Upcoming</span>
                                </div>
                                <p class="text-sm" style="color: var(--text-secondary);">5 push-ups daily • 0/7 days</p>
                                <div class="flex space-x-1 mt-2">
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                    <div class="day-pending w-4 h-4"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Tasks -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Today's Tasks</h3>
                    
                    <div class="space-y-3">
                        <div class="neumorphic-inset p-4 rounded-xl">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="week-current w-8 h-8 flex items-center justify-center">
                                        <i class="fas fa-dumbbell text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium" style="color: var(--text-primary);">3 Push-ups</h4>
                                        <p class="text-sm" style="color: var(--text-secondary);">Week 3 • Day 6</p>
                                    </div>
                                </div>
                                <div class="neumorphic-button px-4 py-2">
                                    <span class="text-sm font-medium" style="color: var(--text-primary);">Complete</span>
                                </div>
                            </div>
                        </div>

                        <div class="neumorphic-inset p-4 rounded-xl">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                        <i class="fas fa-walking text-sm" style="color: var(--wellness);"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium" style="color: var(--text-primary);">2-minute walk</h4>
                                        <p class="text-sm" style="color: var(--text-secondary);">Bonus activity</p>
                                    </div>
                                </div>
                                <div class="neumorphic-button px-4 py-2">
                                    <span class="text-sm font-medium" style="color: var(--text-primary);">Add</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Encouragement Message -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="flex items-start space-x-3">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                            <i class="fas fa-star text-sm" style="color: var(--warning);"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">You're crushing it! 🎉</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">
                                You've completed 19 days straight. Research shows that habits become automatic around 21 days. 
                                You're almost there!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Progress Actions -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="neumorphic-button p-4 text-center">
                        <i class="fas fa-chart-line text-2xl mb-2" style="color: var(--info);"></i>
                        <p class="text-sm font-medium" style="color: var(--text-primary);">View Stats</p>
                    </div>
                    <div class="neumorphic-button p-4 text-center">
                        <i class="fas fa-trophy text-2xl mb-2" style="color: var(--warning);"></i>
                        <p class="text-sm font-medium" style="color: var(--text-primary);">Achievements</p>
                    </div>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Home</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-line text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Progress</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-cog text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Settings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 