<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .toggle-switch {
            width: 48px;
            height: 24px;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: inset 4px 4px 8px rgba(163, 177, 198, 0.6), inset -4px -4px 8px rgba(255, 255, 255, 0.5);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        @media (prefers-color-scheme: dark) {
            .toggle-switch {
                box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.8), inset -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, var(--success), var(--wellness));
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .toggle-switch.active {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .toggle-knob {
            width: 20px;
            height: 20px;
            background: var(--bg-primary);
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .toggle-knob {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }
        
        .toggle-switch.active .toggle-knob {
            left: 26px;
            background: white;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }
        
        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
            .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }
        
        .privacy-banner {
            background: linear-gradient(135deg, var(--success), var(--wellness));
            border-radius: 20px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .privacy-banner {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 16px 0;
            border-bottom: 1px solid;
            border-color: rgba(163, 177, 198, 0.2);
        }
        
        @media (prefers-color-scheme: dark) {
            .setting-item {
                border-color: rgba(255, 255, 255, 0.1);
            }
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">
            
            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">
                
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">Settings</h1>
                        <p class="text-sm" style="color: var(--text-secondary);">Privacy & Preferences</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-info-circle text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>

                <!-- Privacy First Banner -->
                <div class="privacy-banner p-4 mb-6 text-white">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-sm"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Privacy First</h3>
                            <p class="text-sm text-white text-opacity-90">No accounts, no tracking</p>
                        </div>
                    </div>
                    <p class="text-sm text-white text-opacity-80">
                        Your data stays on your device. Optional iCloud sync for backup only.
                    </p>
                </div>

                <!-- iCloud & Backup -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">iCloud & Backup</h3>
                    
                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-cloud text-sm" style="color: var(--info);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">iCloud Sync</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Sync habits across devices</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-file-import text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Import Data</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Import habits from backup</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-file-export text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Export Data</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Export habits for backup</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Notifications</h3>
                    
                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-bell text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Daily Reminders</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Gentle habit reminders</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-fire text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Streaks</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Celebrate streak milestones</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Appearance</h3>
                    
                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-adjust text-sm" style="color: var(--mindfulness);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Mode</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Light, Dark, or System</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-th text-sm" style="color: var(--wellness);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Compact Grid</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Smaller contribution squares</p>
                                </div>
                            </div>
                            <div class="toggle-switch">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-palette text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Color Intensity</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Vivid contribution colors</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- App Preferences -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">App Preferences</h3>
                    
                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-lightbulb text-sm" style="color: var(--info);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Smart Tips</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Helpful habit suggestions</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-seedling text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Growth Insights</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Weekly progress analysis</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-heart text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Motivational Quotes</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Daily inspiration</p>
                                </div>
                            </div>
                            <div class="toggle-switch">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support & Info -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Support & Info</h3>
                    
                    <div class="space-y-3">
                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-question-circle text-lg" style="color: var(--info);"></i>
                                <span style="color: var(--text-primary);">Help & FAQ</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-envelope text-lg" style="color: var(--wellness);"></i>
                                <span style="color: var(--text-primary);">Contact Support</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-star text-lg" style="color: var(--warning);"></i>
                                <span style="color: var(--text-primary);">Rate HabitTest</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-share text-lg" style="color: var(--success);"></i>
                                <span style="color: var(--text-primary);">Share with Friends</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-shield-alt text-lg" style="color: var(--mindfulness);"></i>
                                <span style="color: var(--text-primary);">Privacy Policy</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                </div>

                <!-- App Version -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="text-center">
                        <div class="neumorphic-button w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                            <i class="fas fa-leaf text-2xl" style="color: var(--wellness);"></i>
                        </div>
                        <h3 class="font-semibold" style="color: var(--text-primary);">HabitTest</h3>
                        <p class="text-sm" style="color: var(--text-secondary);">Version 1.0.0</p>
                        <p class="text-xs mt-2" style="color: var(--text-muted);">Built with ❤️ for sustainable habits</p>
                    </div>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Home</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-line text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Progress</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-cog text-lg" style="color: var(--info);"></i>
                        <span class="text-xs mt-1" style="color: var(--info);">Settings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle switch functionality
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });
    </script>
</body>
</html> 
</body>
</html> 