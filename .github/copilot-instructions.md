
# Copilot Instructions for HabitTest

## Project Architecture
- **Static, mobile-first habit tracking app**: All files are static. No build step, no frameworks, no test runner. Open HTML files directly in a browser for development and manual testing.
- **Core logic:** All grid logic and styles are in `habit-grid.js` (self-contained JS+CSS, auto-injects styles, no dependencies).
- **UI screens:** HTML files (e.g., `dashboard.html`, `onboarding.html`, `progress.html`, `settings.html`) serve as entry points and load the grid component.
- **Design system:** All color, spacing, and neumorphic effects are defined in `fitness-analytics-design-system.json` and referenced via CSS custom properties.

## Data Model & Patterns
- **Habit object structure:**
  ```js
  const habit = {
    id: 1,
    name: "Morning Walk",
    timesPerDay: 1,
    startDaysAgo: 30,
    todayCount: 0,
    history: [
      { date: "2025-06-01", level: 3, completed: true, isFuture: false },
      // ...
    ]
  };
  ```
- **Grid levels:** 0 (no activity), 1 (attempted), 2 (low), 3 (medium), 4 (full completion). See `habit-grid.js` and `HABIT-GRID-README.md` for details.
- **Usage:** Instantiate with `const habitGrid = new HabitGrid();` and render with `habitGrid.generateHabitGrid(containerId, habit, options)`.
- **Demo grid:** Use `habitGrid.createDemoGrid(containerId, weeks, completionRate)` for onboarding or previews.
- **Customization:** Override `getTodayDate()` for testing; change grid colors via CSS custom properties or class selectors. All components respond to `prefers-color-scheme` for dark mode.

## Developer Workflow
- **Manual testing only:** Use browser dev tools and open HTML files directly. No CLI, no automated tests.
- **Debugging:**
  - If the grid does not render, check for missing CSS variables or container IDs.
  - Use browser console for JS errors.
- **No external dependencies** except browser and (optionally) Tailwind for layout/typography.

## Design & UX Principles
- **Neumorphic, zen-minimalist UI:**
  - Follow spacing, color, and shadow rules in `fitness-analytics-design-system.json`.
  - Use single-column layouts and clear visual hierarchy.
  - Maintain color contrast and avoid color-only indicators for accessibility.
- **Mobile-first:**
  - All screens/components are optimized for mobile (393px base width, touch targets ≥44px).
  - Horizontal drag-to-scroll is built-in for the grid.

## Integration & Extensibility
- **Component is self-contained:**
  - Only dependency is the browser. Can be dropped into any HTML page with the required CSS variables.
  - See `HABIT-GRID-README.md` for API, troubleshooting, and advanced integration.

## Key Files
- `habit-grid.js`: All grid logic and styles.
- `dashboard.html`, `onboarding.html`, `progress.html`, `settings.html`: Example screens using the grid.
- `fitness-analytics-design-system.json`: Design tokens and layout rules.
- `HABIT-GRID-README.md`: API, data model, and troubleshooting.

---
**For AI agents:**
- Always use and extend `habit-grid.js` for grid logic and UI.
- Reference `HABIT-GRID-README.md` and `fitness-analytics-design-system.json` for all design and data conventions.
- Do not introduce build tools, frameworks, or test runners unless explicitly requested.
- Keep all UI changes mobile-first and consistent with the neumorphic/zen design system.
