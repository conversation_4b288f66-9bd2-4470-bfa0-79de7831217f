<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitTest - Neumorphic Design Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }
        
        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }
        
        .screen-frame {
            width: 200px;
            height: 430px;
            transform: scale(0.8);
            transform-origin: center;
        }
        
        .flow-card {
            background: linear-gradient(135deg, var(--wellness), var(--info));
            border-radius: 20px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .flow-card {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }
        
        .feature-item {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 4px 4px 8px rgba(163, 177, 198, 0.6), -4px -4px 8px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (prefers-color-scheme: dark) {
            .feature-item {
                box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8), -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-ring {
            width: 80px;
            height: 80px;
            background: conic-gradient(var(--success) 84%, var(--bg-primary) 84%);
            border-radius: 50%;
            position: relative;
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-ring {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }
        
        .progress-ring::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: var(--bg-primary);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
        }
        
        @media (prefers-color-scheme: dark) {
            .progress-ring::after {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }
    </style>
</head>
<body class="min-h-screen p-8">
    <div class="max-w-7xl mx-auto">
        
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="neumorphic-button w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <i class="fas fa-leaf text-3xl" style="color: var(--wellness);"></i>
            </div>
            <h1 class="text-4xl font-bold mb-4" style="color: var(--text-primary);">HabitTest</h1>
            <p class="text-xl mb-6" style="color: var(--text-secondary);">Neumorphic Bento Zen Design System</p>
            
            <!-- Key Features -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="feature-item p-6">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-seedling text-lg" style="color: var(--success);"></i>
                    </div>
                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">Mini Habits Philosophy</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Too small to fail approach</p>
                </div>
                <div class="feature-item p-6">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-shield-alt text-lg" style="color: var(--info);"></i>
                    </div>
                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">Privacy First</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">No accounts, local data only</p>
                </div>
                <div class="feature-item p-6">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-th text-lg" style="color: var(--wellness);"></i>
                    </div>
                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">Neumorphic Design</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Soft, tactile interface</p>
                </div>
            </div>
        </div>

        <!-- User Journey Flow -->
        <div class="neumorphic-card p-8 mb-12">
            <h2 class="text-2xl font-bold text-center mb-8" style="color: var(--text-primary);">User Journey Flow</h2>
            <div class="flex flex-wrap justify-center items-center gap-6">
                <div class="flow-card p-4 text-white text-center">
                    <i class="fas fa-rocket text-2xl mb-2"></i>
                    <div class="font-semibold">Onboarding</div>
                    <div class="text-sm opacity-90">Mini habits intro</div>
                </div>
                <i class="fas fa-arrow-right text-2xl" style="color: var(--text-muted);"></i>
                <div class="flow-card p-4 text-white text-center">
                    <i class="fas fa-home text-2xl mb-2"></i>
                    <div class="font-semibold">Dashboard</div>
                    <div class="text-sm opacity-90">Daily tracking</div>
                </div>
                <i class="fas fa-arrow-right text-2xl" style="color: var(--text-muted);"></i>
                <div class="flow-card p-4 text-white text-center">
                    <i class="fas fa-plus text-2xl mb-2"></i>
                    <div class="font-semibold">Create</div>
                    <div class="text-sm opacity-90">New habits</div>
                </div>
                <i class="fas fa-arrow-right text-2xl" style="color: var(--text-muted);"></i>
                <div class="flow-card p-4 text-white text-center">
                    <i class="fas fa-chart-line text-2xl mb-2"></i>
                    <div class="font-semibold">Progress</div>
                    <div class="text-sm opacity-90">Analytics</div>
                </div>
            </div>
        </div>

        <!-- App Screens -->
        <div class="neumorphic-card p-8 mb-12">
            <h2 class="text-2xl font-bold text-center mb-8" style="color: var(--text-primary);">App Screens</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
                
                <!-- Onboarding -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="onboarding.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">Onboarding</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Welcome & philosophy intro</p>
                </div>

                <!-- Dashboard -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="dashboard.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">Dashboard</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">GitHub-style progress grid</p>
                </div>

                <!-- Create Habit -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="create-habit.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">Create Habit</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Guided habit creation</p>
                </div>

                <!-- 4-Week Plan -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="four-week-plan.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">4-Week Plan</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Structured beginner programs</p>
                </div>

                <!-- Progress -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="progress.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">Progress</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Analytics & insights</p>
                </div>

                <!-- Settings -->
                <div class="text-center">
                    <div class="screen-frame">
                        <iframe src="settings.html" class="w-full h-full rounded-[2rem] border-0"></iframe>
                    </div>
                    <h3 class="font-semibold mt-4 mb-2" style="color: var(--text-primary);">Settings</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">Privacy & preferences</p>
                </div>
            </div>
        </div>

        <!-- Key Features -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            
            <!-- Design Features -->
            <div class="neumorphic-card p-8">
                <h3 class="text-xl font-bold mb-6" style="color: var(--text-primary);">Neumorphic Design Features</h3>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-palette text-sm" style="color: var(--warning);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Soft UI Elements</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Extruded buttons and cards</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-sun text-sm" style="color: var(--warning);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Light/Dark Mode</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Automatic theme switching</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-cube text-sm" style="color: var(--info);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">3D Depth</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Layered shadow effects</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-hand-pointer text-sm" style="color: var(--success);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Tactile Feedback</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Interactive state changes</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- App Philosophy -->
            <div class="neumorphic-card p-8">
                <h3 class="text-xl font-bold mb-6" style="color: var(--text-primary);">App Philosophy</h3>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-feather text-sm" style="color: var(--wellness);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Too Small to Fail</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Micro habits for lasting change</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-shield-alt text-sm" style="color: var(--info);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Privacy First</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">No accounts or tracking</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-heart text-sm" style="color: var(--warning);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Built for Real Life</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Handles interruptions gracefully</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="neumorphic-button w-10 h-10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-seedling text-sm" style="color: var(--success);"></i>
                        </div>
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">Sustainable Growth</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">Long-term behavior change</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Implementation -->
        <div class="neumorphic-card p-8 mb-12">
            <h3 class="text-xl font-bold mb-6" style="color: var(--text-primary);">Technical Implementation</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="feature-item p-4 text-center">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                        <i class="fab fa-html5 text-lg" style="color: var(--warning);"></i>
                    </div>
                    <h4 class="font-medium mb-2" style="color: var(--text-primary);">HTML5</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Semantic structure</p>
                </div>
                <div class="feature-item p-4 text-center">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                        <i class="fab fa-css3-alt text-lg" style="color: var(--info);"></i>
                    </div>
                    <h4 class="font-medium mb-2" style="color: var(--text-primary);">Tailwind CSS</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Utility-first styling</p>
                </div>
                <div class="feature-item p-4 text-center">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                        <i class="fas fa-icons text-lg" style="color: var(--success);"></i>
                    </div>
                    <h4 class="font-medium mb-2" style="color: var(--text-primary);">FontAwesome</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Icon library</p>
                </div>
                <div class="feature-item p-4 text-center">
                    <div class="neumorphic-button w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                        <i class="fas fa-mobile-alt text-lg" style="color: var(--wellness);"></i>
                    </div>
                    <h4 class="font-medium mb-2" style="color: var(--text-primary);">iPhone 16</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Native simulation</p>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="neumorphic-card p-8">
            <h3 class="text-xl font-bold text-center mb-8" style="color: var(--text-primary);">Prototype Statistics</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="progress-ring mx-auto mb-4 flex items-center justify-center">
                        <span class="text-lg font-bold relative z-10" style="color: var(--text-primary);">6</span>
                    </div>
                    <h4 class="font-semibold" style="color: var(--text-primary);">Screens</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Complete flow</p>
                </div>
                <div class="text-center">
                    <div class="progress-ring mx-auto mb-4 flex items-center justify-center">
                        <span class="text-lg font-bold relative z-10" style="color: var(--text-primary);">100%</span>
                    </div>
                    <h4 class="font-semibold" style="color: var(--text-primary);">Responsive</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Mobile optimized</p>
                </div>
                <div class="text-center">
                    <div class="progress-ring mx-auto mb-4 flex items-center justify-center">
                        <span class="text-lg font-bold relative z-10" style="color: var(--text-primary);">2</span>
                    </div>
                    <h4 class="font-semibold" style="color: var(--text-primary);">Themes</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Light & dark</p>
                </div>
                <div class="text-center">
                    <div class="progress-ring mx-auto mb-4 flex items-center justify-center">
                        <span class="text-lg font-bold relative z-10" style="color: var(--text-primary);">∞</span>
                    </div>
                    <h4 class="font-semibold" style="color: var(--text-primary);">Habits</h4>
                    <p class="text-sm" style="color: var(--text-secondary);">Unlimited tracking</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 