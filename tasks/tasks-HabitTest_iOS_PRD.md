# HabitTest iOS Implementation Tasks

Based on the Product Requirements Document and existing web prototype, this task list guides the implementation of the HabitTest iOS app using Swift 6, SwiftUI, and SwiftData.

## Relevant Files

### Core Data Models
- `Models/Habit.swift` - SwiftData model for habit entities with properties, relationships, and computed values
- `Models/HabitRecord.swift` - SwiftData model for daily habit completion records
- `Models/HabitCategory.swift` - Enum and utilities for habit categorization
- `Models/HabitFrequency.swift` - Enum for habit frequency settings (daily, weekdays, custom)

### Data Layer
- `Services/HabitDataService.swift` - Core data operations and business logic for habit management
- `Services/HabitGridService.swift` - Service for generating and managing habit grid data (based on habit-grid.js)
- `ViewModels/HabitListViewModel.swift` - ViewModel for dashboard habit list management
- `ViewModels/HabitDetailViewModel.swift` - ViewModel for individual habit operations
- `ViewModels/HabitGridViewModel.swift` - ViewModel for GitHub-style progress grid

### UI Components
- `Views/Components/NeumorphicCard.swift` - Reusable neumorphic card component
- `Views/Components/NeumorphicButton.swift` - Reusable neumorphic button component
- `Views/Components/HabitGridView.swift` - GitHub-style habit progress grid component (core reusable component)
- `Views/Components/CircularProgressView.swift` - Circular progress indicator component
- `Views/Components/HabitRowView.swift` - Individual habit row component for lists

### Main Views
- `Views/DashboardView.swift` - Main dashboard view (based on dashboard.html)
- `Views/CreateHabitView.swift` - Habit creation form view (based on create-habit.html)
- `Views/EditHabitView.swift` - Habit editing form view (similar to create-habit)
- `Views/ArchiveView.swift` - Archived habits view (based on archive.html)
- `Views/ProgressView.swift` - Progress analytics view (based on progress.html) - Low priority

### Design System
- `DesignSystem/NeumorphicStyle.swift` - SwiftUI neumorphic design system implementation
- `DesignSystem/Colors.swift` - Color palette from fitness-analytics-design-system.json
- `DesignSystem/Typography.swift` - Typography system matching design guidelines
- `DesignSystem/Spacing.swift` - Spacing and layout constants

### App Structure
- `HabitTestApp.swift` - Main app entry point with SwiftData configuration
- `ContentView.swift` - Root view with tab navigation
- `Navigation/TabBarView.swift` - Custom tab bar implementation

### Tests
- `Tests/Models/HabitTests.swift` - Unit tests for Habit model
- `Tests/Services/HabitDataServiceTests.swift` - Unit tests for data service
- `Tests/ViewModels/HabitListViewModelTests.swift` - Unit tests for view models
- `Tests/UI/HabitGridViewTests.swift` - UI tests for habit grid component

### Notes
- Unit tests should be placed in the `Tests/` directory with corresponding folder structure
- Use `cmd+u` in Xcode to run all tests, or right-click specific test files to run individually
- SwiftData models require `@Model` macro and should be configured in the main app file
- Follow MVVM pattern with ViewModels handling business logic and Views focusing on UI

## Tasks

- [ ] 1.0 Setup Project Foundation and Data Models
- [ ] 2.0 Implement Core Data Services and Business Logic
- [ ] 3.0 Create Neumorphic Design System Components
- [ ] 4.0 Build Dashboard View (Main Interface)
- [ ] 5.0 Implement Habit Management Views (Create/Edit)
- [ ] 6.0 Develop GitHub-Style Habit Grid Component
- [ ] 7.0 Create Archive and Progress Views
- [ ] 8.0 Add Navigation and Polish